import type {
  ICalculateResultsRequest,
  ICalculateResultsResponse,
  ICalculateResultsWithPersonalityRequest,
  ICalculateResultsWithPersonalityResponse,
  IGetCurrentUserTypeRequest,
  IGetCurrentUserTypeResponse,
  IGetQuestionsResponse,
  IGetQuestionsResponseData,
  IGetTestHistoryResponse,
  ISubmitAnswersRequest,
  ISubmitAnswersResponse,
} from './types/question'
import { http } from '@/utils/http'

/**
 * 获取所有问题
 * @returns 问题列表
 */
export function getQuestions() {
  return http.get<IGetQuestionsResponseData>('/questions')
}

/**
 * 提交用户答案
 * @param userId 用户ID
 * @param answers 答案数组 (32个整数，0-3表示选项索引)
 * @param remark 可选备注
 */
export function submitAnswers(userId: number, answers: number[], remark?: string) {
  const data: ISubmitAnswersRequest = {
    userId,
    answers,
    remark,
  }
  console.log(JSON.stringify(data))
  return http.post<ISubmitAnswersResponse>('/questions/user-answer-records', data)
}

/**
 * 计算性格测试结果
 * @param answers 答案数组 (32个整数，0-3表示选项索引)
 */
export function calculateResults(answers: number[]) {
  const data: ICalculateResultsRequest = {
    answers,
  }
  return http.post<ICalculateResultsResponse>('/questions/personality-results/calculate', data)
}

/**
 * 获取当前用户的性格类型
 * @param userId 用户ID
 */
export function getCurrentUserType(userId: number) {
  const data: IGetCurrentUserTypeRequest = {
    userId,
  }
  return http.post<IGetCurrentUserTypeResponse>('/questions/user-answer-records/current-type', data)
}

/**
 * 获取用户测试历史
 * @param userId 用户ID
 * @param page 页码
 * @param pageSize 每页大小
 */
export function getTestHistory(userId: number, page: number = 1, pageSize: number = 10) {
  return http.get<IGetTestHistoryResponse>('/questions/user-answer-records/history', {
    userId,
    page,
    pageSize,
  })
}

/**
 * 根据记录ID获取测试结果详情
 * @param recordId 记录ID
 */
export function getTestResultById(recordId: number) {
  return http.get<ICalculateResultsResponse>(`/questions/user-answer-records/${recordId}/result`)
}

/**
 * 计算MBTI和DISC结果并返回完整人格测试结果
 * @param answers 答案数组 (32个整数，0-3表示选项索引)
 */
export function calculateResultsWithPersonality(answers: number[]) {
  const data: ICalculateResultsWithPersonalityRequest = {
    answers,
  }
  return http.post<ICalculateResultsWithPersonalityResponse>('/questions/calculate-results-with-personality', data)
}
