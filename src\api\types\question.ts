/**
 * 问题选项接口
 */
export interface IQuestionOption {
  id: number
  letter: string // A, B, C, D
  text: string // 选项内容
  isSelect?: number // 是否选中 (0 或 1)
}

/**
 * 问题接口
 */
export interface IQuestion {
  id: number
  text: string // 问题标题
  dimension: string // 维度
  options: IQuestionOption[] // 选项列表
  userAnswer?: string | IQuestionOption | IQuestionOption[] // 用户答案
}

/**
 * 获取问题列表响应
 */
export interface IGetQuestionsResponse {
  data: IQuestion[]
  timestamp: string
}

/**
 * 获取问题列表响应（经过拦截器处理后的格式）
 */
export type IGetQuestionsResponseData = IQuestion[]

/**
 * 提交答案请求
 */
export interface ISubmitAnswersRequest {
  userId: number
  answers: number[] // 32个整数数组，每个值为0-3表示选项索引
  remark?: string // 可选备注
}

/**
 * 提交答案响应
 */
export interface ISubmitAnswersResponse {
  id: number
  userId: number
  answers: number[]
  mbtiType: string
  discType: string
  finalType: string
  mbtiScores: {
    E: number
    I: number
    S: number
    N: number
    T: number
    F: number
    J: number
    P: number
  }
  discScores: {
    D: number
    I: number
    S: number
    C: number
  }
  dimensionScores: {
    [key: string]: number
  }
  isCurrent: boolean
  remark?: string
  createdAt: string
  updatedAt: string
}

/**
 * 性格类型结果
 */
export interface IPersonalityResult {
  mbtiType: string // MBTI类型，如 "INTJ"
  discType: string // DISC类型，如 "D"
  finalType: string // 最终综合类型
  mbtiScores: {
    E: number
    I: number
    S: number
    N: number
    T: number
    F: number
    J: number
    P: number
  }
  discScores: {
    D: number
    I: number
    S: number
    C: number
  }
  dimensionScores: {
    [key: string]: number
  }
  traits?: string[] // 性格特征
  strengths?: string[] // 优势
  blindSpots?: string[] // 盲点
  suggestions?: string[] // 建议
  description?: string // 详细描述
}

/**
 * 计算结果请求
 */
export interface ICalculateResultsRequest {
  answers: number[] // 32个整数数组
}

/**
 * 计算结果响应
 */
export interface ICalculateResultsResponse {
  result: IPersonalityResult
}

/**
 * 获取当前用户类型请求
 */
export interface IGetCurrentUserTypeRequest {
  userId: number
}

/**
 * 获取当前用户类型响应
 */
export interface IGetCurrentUserTypeResponse {
  hasResult: boolean
  result?: IPersonalityResult
  lastTestDate?: string
}

/**
 * 测试历史记录
 */
export interface ITestHistoryRecord {
  id: number
  userId: number
  answers: number[]
  mbtiType: string
  discType: string
  finalType: string
  mbtiScores: {
    E: number
    I: number
    S: number
    N: number
    T: number
    F: number
    J: number
    P: number
  }
  discScores: {
    D: number
    I: number
    S: number
    C: number
  }
  dimensionScores: {
    [key: string]: number
  }
  isCurrent: boolean
  remark?: string
  createdAt: string
  updatedAt: string
}

/**
 * 分页信息
 */
export interface IPagination {
  page: number
  pageSize: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

/**
 * 获取测试历史响应
 */
export interface IGetTestHistoryResponse {
  data: ITestHistoryRecord[]
  pagination: IPagination
}

/**
 * 新的计算结果并返回完整人格测试结果请求
 */
export interface ICalculateResultsWithPersonalityRequest {
  answers: number[] // 32个整数数组，0-3表示选项索引
}

/**
 * 人格测试完整结果
 */
export interface IPersonalityTestResult {
  typeCode: string // 人格类型代码（如：ISTJ-D）
  title: string // 人格类型标题（如：规则凝聚者）
  coreTraits: string // 核心特质描述
  strengths: string // 优势特点
  sceneMatch: string // 适用场景
  blindSpots: string // 盲点/弱点
  suggestions: string // 改进建议
  symbol: string // 象征符号
  lifePath: string // 人生路径
  valueGuide: string // 价值指导（包含HTML标签）
}

/**
 * 新的计算结果并返回完整人格测试结果响应
 */
export interface ICalculateResultsWithPersonalityResponse {
  mbti: {
    E: number
    I: number
    S: number
    N: number
    T: number
    F: number
    J: number
    P: number
  }
  disc: {
    D: number
    I: number
    S: number
    C: number
  }
  mbtiType: string // MBTI类型（如：ISTJ）
  discType: string // DISC主导风格（如：D）
  finalType: string // 最终组合类型（如：ISTJ-D）
  dimensionScores: {
    [key: string]: number // 各维度详细得分
  }
  personalityResult: IPersonalityTestResult // 人格测试结果部分
}
