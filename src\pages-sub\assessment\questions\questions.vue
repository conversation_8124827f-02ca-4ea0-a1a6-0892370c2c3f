<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '64心智模型测评',
      },
    }
  </route>

<script setup lang="ts">
import type { IQuestion } from '@/api/types/question'
import { onMounted, ref } from 'vue'
import { calculateResults, getQuestions, submitAnswers } from '@/api/question'
import { useUserStore } from '@/store/user'

const topBanner = ''
const chooseonImg = 'https://lcode.kvxin.top/dubai.jpg'
const chooseonImg2 = 'https://lcode.kvxin.top/dubai.jpg'

const totalNum = ref(32) // 固定32道题
const currentIndex = ref(0)
const questions = ref<IQuestion[]>([])
const answers = ref<number[]>(Array.from({ length: 32 })) // 存储答案索引，-1表示未选择
const loading = ref(false)
const submitting = ref(false)

// 用户store
const userStore = useUserStore()

// 获取用户ID
function getUserId() {
  return userStore.userDetails?.id || 1
}

// 加载问题数据
async function loadQuestions() {
  try {
    loading.value = true
    const response = await getQuestions()
    if (response && response.data && response.data.length > 0) {
      questions.value = response.data.slice(0, 32) // 确保只有32道题
      totalNum.value = questions.value.length

      // 初始化选项状态
      questions.value.forEach((question) => {
        question.options?.forEach((option) => {
          option.isSelect = 0
        })
      })
    }
  }
  catch (error) {
    console.error('加载问题失败:', error)
    uni.showToast({
      title: '加载问题失败，请重试',
      icon: 'none',
    })
  }
  finally {
    loading.value = false
  }
}

// 提交答案
async function submitData() {
  // 检查是否所有题目都已回答
  for (let i = 0; i < answers.value.length; i++) {
    if (answers.value[i] === -1) {
      uni.showToast({
        title: `请完成第${i + 1}题后提交！`,
        icon: 'none',
      })
      currentIndex.value = i
      return
    }
  }

  try {
    submitting.value = true
    const userId = getUserId()

    // 提交答案
    const submitResponse = await submitAnswers(userId, answers.value)

    if (submitResponse.data) {
      // 提交成功后直接跳转到结果页面，传递结果数据
      uni.navigateTo({
        url: `/pages-sub/assessment/questions/results?result=${encodeURIComponent(JSON.stringify(submitResponse.data))}`,
      })
    }
  }
  catch (error) {
    console.error('提交答案失败:', error)
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none',
    })
  }
  finally {
    submitting.value = false
  }
}

// 单选题选择
function singChoose(questionIndex: number, optionIndex: number) {
  const question = questions.value[questionIndex]
  if (!question)
    return

  // 清除该题目的所有选项选中状态
  question.options?.forEach((option) => {
    option.isSelect = 0
  })

  // 设置当前选项为选中状态
  if (question.options?.[optionIndex]) {
    question.options[optionIndex].isSelect = 1
    answers.value[questionIndex] = optionIndex // 存储选项索引
  }
}
// 滑动事件处理
function eventHandle(e: any) {
  currentIndex.value = e.detail.current
}

// 上一题
function back(index: number) {
  if (index <= 0)
    return
  currentIndex.value = index - 1
}

// 下一题
function next(index: number) {
  if (index >= totalNum.value - 1)
    return
  currentIndex.value = index + 1
}

// 检查登录状态
function checkLoginStatus() {
  if (!userStore.isLoggedIn) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再进行64人格测评',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login',
          })
        }
        else {
          uni.navigateBack()
        }
      },
    })
    return false
  }
  return true
}

// 页面加载时检查登录状态并获取问题
onMounted(() => {
  if (checkLoginStatus()) {
    loadQuestions()
  }
})
</script>

<template>
  <view class="page-main">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-text">
        正在加载问题...
      </view>
    </view>

    <!-- 主要内容 -->
    <view v-else class="content-container">
      <view class="topbox">
        <image class="topimg" :src="topBanner" />
        <view class="imgtext">
          性格测评 - 了解真实的自己
        </view>
      </view>

      <!-- 进度条 -->
      <view class="progress-container">
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: `${((currentIndex + 1) / totalNum) * 100}%` }" />
        </view>
        <view class="progress-text">
          {{ currentIndex + 1 }} / {{ totalNum }}
        </view>
      </view>

      <swiper
        class="swipercard" previous-margin="0" next-margin="0" :circular="false" :autoplay="false"
        :current="currentIndex" @change="eventHandle"
      >
        <swiper-item v-for="(item, index) in questions" :key="item.id" class="swiperitem">
          <view class="itembox">
            <view class="box-hd">
              <view class="hdname">
                当前第<view class="text1">
                  {{ index + 1 }}
                </view>道题
              </view>
              <view class="hdnum">
                共{{ totalNum }}道题
              </view>
            </view>
            <view class="contentbox">
              <view class="boxtitle">
                <text class="textl">
                  {{ index + 1 }}、
                </text>
                <text class="textr">
                  {{ item.text }}
                </text>
              </view>

              <!-- 单选题 -->
              <template v-if="item.options && item.options.length > 0">
                <view v-for="(option, optionIndex) in item.options" :key="option.id" class="boxbody">
                  <view class="chooseitem" @click="singChoose(index, optionIndex)">
                    <image v-if="option.isSelect" class="sinchoose-on" :src="chooseonImg" />
                    <view v-else class="sinchoose" />
                    <view class="bodyr">
                      {{ option.letter }}、{{ option.text }}
                    </view>
                  </view>
                </view>
              </template>
            </view>

            <!-- 底部按钮 -->
            <view class="footbtn">
              <view class="ftbtn1" :class="{ disabled: index === 0 }" @click="back(index)">
                上一题
              </view>
              <view v-if="(index + 1) < totalNum" class="ftbtn1" @click="next(index)">
                下一题
              </view>
              <view v-else class="ftbtn2" :class="{ submitting }" @click="submitData">
                {{ submitting ? '提交中...' : '提交' }}
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

  <style scoped>
  .page-main {
  width: 100%;
  height: 100vh;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-text {
  font-size: 32rpx;
  color: #666666;
}

.content-container {
  width: 100%;
  height: 100vh;
}

.progress-container {
  padding: 20rpx 48rpx;
  background: #ffffff;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1fa474 0%, #26d0ce 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 24rpx;
  color: #666666;
  margin-top: 8rpx;
}

.topbox {
  width: 100%;
  height: 140rpx;
  position: relative;
}

.topbox .topimg {
  width: 100%;
  height: 100%;
}

.topbox .imgtext {
  position: absolute;
  bottom: 60rpx;
  left: 58rpx;
  font-size: 36rpx;
  font-weight: normal;
  color: #ffffff;
  line-height: 36rpx;
  text-shadow: 0rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.swipercard {
  width: 100%;
  height: calc(100vh - 380rpx);
  background: #ffffff;
}

.itembox {
  width: calc(100% - 96rpx);
  padding: 32rpx 48rpx;
}

.box-hd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 4rpx dashed #f0f0f0;
  padding-bottom: 16rpx;
  padding-left: 16rpx;
}

.hdname {
  width: 400rpx;
  font-size: 28rpx;
  display: flex;
  align-items: flex-start;
  font-weight: 500;
  color: #666666;
  line-height: 40rpx;
}

.text1 {
  color: #1fa474;
  font-size: 40rpx;
  line-height: 32rpx;
}

.hdnum {
  font-size: 28rpx;
  font-weight: 400;
  color: #666666;
  line-height: 42rpx;
}

.contentbox {
  font-size: 30rpx;
  color: #333333;
  margin-top: 48rpx;
}

.boxtitle .textl {
  width: 50rpx;
  height: 34rpx;
  background: linear-gradient(90deg, #c3ffda 0%, #ffffff 100%);
}

.boxbody {
  padding-left: 40rpx;
  line-height: 64rpx;
  margin: 16rpx 0;
}

.chooseitem {
  display: flex;
  align-items: center;
}

.sinchoose {
  width: 28rpx;
  height: 28rpx;
  margin-right: 16rpx;
  border-radius: 50%;
  background: #ffffff;
  border: 2rpx solid #bfbdbd;
}

.sinchoose2 {
  border-radius: 6rpx;
}

.sinchoose-on {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.writeitem textarea {
  min-height: 164rpx;
  margin: 24rpx auto;
  padding: 16rpx;
  border: 2rpx solid #ebebeb;
  border-radius: 4px;
  font-size: 30rpx;
  color: #333333;
}

.footbtn {
  display: flex;
  justify-content: space-between;
  margin-top: 112rpx;
  padding: 0 24rpx;
}

.ftbtn1 {
  width: 270rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border: 2rpx solid #1fa474;
  font-size: 30rpx;
  font-weight: 500;
  color: #1fa474;
}

.ftbtn2 {
  width: 270rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #1fa474;
  font-size: 30rpx;
  font-weight: 500;
  color: #ffffff;
}

.ftbtn1.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.ftbtn2.submitting {
  opacity: 0.7;
  pointer-events: none;
}
</style>
