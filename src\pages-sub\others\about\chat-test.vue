<route lang="json5">
{
  style: {
    navigationBarTitleText: '聊天API测试',
  },
}
</route>

<script lang="ts" setup>
import { ref } from 'vue'
import { ChatAPI } from '@/api/chat'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'ChatTestPage',
})

const userStore = useUserStore()
const testResults = ref<string[]>([])
const isLoading = ref(false)

// 添加测试结果
function addResult(message: string, isError = false) {
  const timestamp = new Date().toLocaleTimeString()
  const prefix = isError ? '❌ ERROR' : '✅ SUCCESS'
  testResults.value.push(`[${timestamp}] ${prefix}: ${message}`)
}

// 清空测试结果
function clearResults() {
  testResults.value = []
}

// 测试用户登录状态
async function testUserAuth() {
  isLoading.value = true
  addResult('开始测试用户认证状态...')

  try {
    const token = uni.getStorageSync('token')
    if (!token) {
      addResult('用户未登录，请先登录', true)
      return
    }

    addResult(`找到用户token: ${token.substring(0, 20)}...`)
    addResult('用户认证测试通过')
  }
  catch (error: any) {
    addResult(`用户认证测试失败: ${error.message}`, true)
  }
  finally {
    isLoading.value = false
  }
}

// 测试创建对话
async function testCreateConversation() {
  isLoading.value = true
  addResult('开始测试创建对话...')

  try {
    const response = await ChatAPI.createConversation({
      title: '测试对话',
      region: 'zh',
      metadata: {
        topic: 'test',
        difficulty: 'beginner',
      },
    })

    addResult(`成功创建对话: ${response.id}`)
    addResult(`对话标题: ${response.title}`)
    addResult(`创建时间: ${response.createdAt}`)
  }
  catch (error: any) {
    addResult(`创建对话失败: ${error.message}`, true)
  }
  finally {
    isLoading.value = false
  }
}

// 测试获取对话列表
async function testGetConversations() {
  isLoading.value = true
  addResult('开始测试获取对话列表...')

  try {
    const response = await ChatAPI.getUserConversations(1, 10)
    addResult(`获取对话列表成功`)
    addResult(`对话总数: ${response.pagination.total}`)
    addResult(`当前页面: ${response.pagination.page}`)
    addResult(`每页大小: ${response.pagination.pageSize}`)

    if (response.data.length > 0) {
      addResult(`第一个对话: ${response.data[0].title}`)
    }
  }
  catch (error: any) {
    addResult(`获取对话列表失败: ${error.message}`, true)
  }
  finally {
    isLoading.value = false
  }
}

// 测试发送消息
async function testSendMessage() {
  isLoading.value = true
  addResult('开始测试发送消息...')

  try {
    // 先获取对话列表
    const conversationsResponse = await ChatAPI.getUserConversations(1, 1)

    if (conversationsResponse.data.length === 0) {
      addResult('没有找到对话，先创建一个对话...', true)
      await testCreateConversation()
      return
    }

    const conversationId = conversationsResponse.data[0].id
    addResult(`使用对话: ${conversationId}`)

    // 发送测试消息
    const messageResponse = await ChatAPI.sendMessage({
      conversationId,
      content: '你好，这是一个测试消息',
      type: 'text',
      streaming: false,
      metadata: {
        topic: 'test',
      },
    })

    addResult(`发送消息成功`)
    addResult(`用户消息ID: ${messageResponse.userMessage.id}`)
    addResult(`AI消息ID: ${messageResponse.assistantMessage.id}`)
    addResult(`AI回复: ${messageResponse.assistantMessage.content.substring(0, 50)}...`)

    // 验证字段名是否正确
    if (messageResponse.assistantMessage) {
      addResult('✅ assistantMessage字段存在，字段名修复成功')
    }
    else {
      addResult('❌ assistantMessage字段不存在，字段名修复失败', true)
    }

    // 检查其他字段
    if (messageResponse.userMessage.cozeMessageId !== undefined) {
      addResult('✅ cozeMessageId字段存在')
    }

    if (messageResponse.assistantMessage.cozeMessageId) {
      addResult(`✅ AI消息cozeMessageId: ${messageResponse.assistantMessage.cozeMessageId}`)
    }
  }
  catch (error: any) {
    addResult(`发送消息失败: ${error.message}`, true)
  }
  finally {
    isLoading.value = false
  }
}

// 运行所有测试
async function runAllTests() {
  clearResults()
  addResult('开始运行所有测试...')

  await testUserAuth()
  await testGetConversations()
  await testCreateConversation()
  await testSendMessage()

  addResult('所有测试完成')
}
</script>

<template>
  <view class="test-page">
    <!-- 标题 -->
    <view class="header">
      <text class="title">
        聊天API测试
      </text>
      <text class="subtitle">
        测试后端AI聊天接口是否正常工作
      </text>
    </view>

    <!-- 用户信息 -->
    <view class="user-info">
      <text class="info-title">
        用户信息
      </text>
      <text class="info-text">
        登录状态: {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
      </text>
      <text v-if="userStore.userDetails" class="info-text">
        用户名: {{ userStore.userDetails.name }}
      </text>
      <text v-if="userStore.token" class="info-text">
        Token: {{ userStore.token.substring(0, 20) }}...
      </text>
    </view>

    <!-- 测试按钮 -->
    <view class="test-buttons">
      <button
        class="test-btn"
        :disabled="isLoading"
        @tap="testUserAuth"
      >
        测试用户认证
      </button>

      <button
        class="test-btn"
        :disabled="isLoading"
        @tap="testGetConversations"
      >
        测试获取对话列表
      </button>

      <button
        class="test-btn"
        :disabled="isLoading"
        @tap="testCreateConversation"
      >
        测试创建对话
      </button>

      <button
        class="test-btn"
        :disabled="isLoading"
        @tap="testSendMessage"
      >
        测试发送消息
      </button>

      <button
        class="test-btn primary"
        :disabled="isLoading"
        @tap="runAllTests"
      >
        运行所有测试
      </button>

      <button
        class="test-btn secondary"
        :disabled="isLoading"
        @tap="clearResults"
      >
        清空结果
      </button>
    </view>

    <!-- 测试结果 -->
    <view class="test-results">
      <text class="results-title">
        测试结果
      </text>

      <view v-if="isLoading" class="loading">
        <text>正在执行测试...</text>
      </view>

      <view v-if="testResults.length === 0 && !isLoading" class="no-results">
        <text>暂无测试结果</text>
      </view>

      <view v-for="(result, index) in testResults" :key="index" class="result-item">
        <text class="result-text" :class="{ error: result.includes('ERROR') }">
          {{ result }}
        </text>
      </view>
    </view>
  </view>
</template>

<style scoped>
.test-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: white;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  text-align: center;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 14px;
  color: #666;
  display: block;
}

.user-info {
  background: white;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.info-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.info-text {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 5px;
}

.test-buttons {
  background: white;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.test-btn {
  width: 100%;
  padding: 15px;
  margin-bottom: 10px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  background: #007aff;
  color: white;
  text-align: center;
}

.test-btn:disabled {
  background: #ccc;
  color: #999;
}

.test-btn.primary {
  background: #34c759;
}

.test-btn.secondary {
  background: #ff3b30;
}

.test-results {
  background: white;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.results-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15px;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

.no-results {
  text-align: center;
  padding: 20px;
  color: #999;
}

.result-item {
  margin-bottom: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 5px;
  border-left: 4px solid #28a745;
}

.result-item:has(.error) {
  border-left-color: #dc3545;
}

.result-text {
  font-size: 12px;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
}

.result-text.error {
  color: #dc3545;
}
</style>
