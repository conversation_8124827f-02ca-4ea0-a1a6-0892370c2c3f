<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '团队邀请',
  },
}
</route>

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'InvitePage',
})

// 用户store
const userStore = useUserStore()

// 邀请码和状态
const inviteCode = ref('')
const loading = ref(false)
const error = ref('')
const copied = ref(false)
const inviteStats = ref({
  totalCount: 0,
  activeCount: 0,
  teamCount: 0,
})

// 输入的邀请码
const inputCode = ref('')
const processing = ref(false)

// 是否显示接受邀请码的表单
const showAcceptForm = ref(false)

// 判断用户是否有团队权限
const hasTeamPermission = computed(() => {
  return userStore.isLoggedIn
    && userStore.userDetails
    && userStore.userDetails.hasTeamPermission
})

// 获取我的邀请码
async function getMyInviteCode() {
  if (!userStore.isLoggedIn) {
    error.value = '请先登录'
    return
  }

  loading.value = true
  error.value = ''

  try {
    // 这里应该是真实API调用，这里模拟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟API响应
    if (hasTeamPermission.value) {
      inviteCode.value = `TEAM${Math.random().toString(36).substring(2, 10).toUpperCase()}`

      // 模拟邀请数据
      inviteStats.value = {
        totalCount: Math.floor(Math.random() * 10),
        activeCount: Math.floor(Math.random() * 5),
        teamCount: Math.floor(Math.random() * 3),
      }
    }
    else {
      error.value = '您需要开通团队权限才能获取邀请码'
    }
  }
  catch (err) {
    console.error('获取邀请码失败:', err)
    error.value = '获取邀请码失败，请重试'
  }
  finally {
    loading.value = false
  }
}

// 复制邀请码
function copyInviteCode() {
  if (!inviteCode.value)
    return

  uni.setClipboardData({
    data: inviteCode.value,
    success: () => {
      copied.value = true
      setTimeout(() => {
        copied.value = false
      }, 3000)
    },
  })
}

// 分享给微信好友
function shareToWechat() {
  if (!inviteCode.value)
    return

  // #ifdef MP-WEIXIN
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline'],
  })
  // #endif

  // 在其他平台，提示用户复制
  // #ifndef MP-WEIXIN
  uni.showModal({
    title: '分享提示',
    content: '请复制邀请码分享给好友',
    confirmText: '复制',
    success: (res) => {
      if (res.confirm) {
        copyInviteCode()
      }
    },
  })
  // #endif
}

// 切换显示接受邀请码表单
function toggleAcceptForm() {
  showAcceptForm.value = !showAcceptForm.value
}

// 接受邀请码
async function acceptInviteCode() {
  if (!inputCode.value) {
    uni.showToast({
      title: '请输入邀请码',
      icon: 'none',
    })
    return
  }

  if (!userStore.isLoggedIn) {
    uni.showModal({
      title: '需要登录',
      content: '接受邀请需要先登录账号',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login',
          })
        }
      },
    })
    return
  }

  processing.value = true

  try {
    // 这里应该是真实API调用，这里模拟
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 模拟成功
    if (inputCode.value.length >= 8) {
      // 更新用户信息，假设接受邀请成功后会获得团队权限
      if (userStore.userDetails) {
        const updatedUser = {
          ...userStore.userDetails,
          hasTeamPermission: true,
        }

        // 更新用户信息
        userStore.setUserDetails(updatedUser, userStore.token)
      }

      // 提示成功
      uni.showToast({
        title: '邀请接受成功',
        icon: 'success',
      })

      // 关闭表单，刷新邀请码
      showAcceptForm.value = false
      inputCode.value = ''

      // 如果成功获取团队权限，尝试获取邀请码
      if (!inviteCode.value) {
        setTimeout(() => {
          getMyInviteCode()
        }, 1000)
      }
    }
    else {
      uni.showToast({
        title: '邀请码无效',
        icon: 'error',
      })
    }
  }
  catch (err) {
    console.error('接受邀请失败:', err)
    uni.showToast({
      title: '接受失败，请重试',
      icon: 'none',
    })
  }
  finally {
    processing.value = false
  }
}

// 前往会员购买页面
function goToMembership() {
  uni.navigateTo({
    url: '/pages-sub/user/membership/membership',
  })
}

// 页面加载时，如果已经登录且有团队权限，则获取邀请码
onMounted(() => {
  if (userStore.isLoggedIn) {
    if (hasTeamPermission.value) {
      getMyInviteCode()
    }
  }
})
</script>

<template>
  <view class="invite-container">
    <!-- 顶部卡片 -->
    <view class="header-card">
      <view class="header-content">
        <text class="header-title">
          团队邀请
        </text>
        <text class="header-subtitle">
          邀请好友加入，共享专业心理服务
        </text>
      </view>
    </view>

    <!-- 我的邀请码 -->
    <view v-if="userStore.isLoggedIn" class="invite-code-section">
      <text class="section-title">
        我的邀请码
      </text>

      <!-- 邀请码显示 -->
      <view v-if="!loading && !error && inviteCode" class="invite-code-card">
        <view class="code-display">
          <text class="code-value">
            {{ inviteCode }}
          </text>
          <view class="code-actions">
            <view class="action-btn copy-btn" :class="{ copied }" @tap="copyInviteCode">
              <text class="action-icon">
                📋
              </text>
              <text class="action-text">
                {{ copied ? '已复制' : '复制' }}
              </text>
            </view>
            <view class="action-btn share-btn" @tap="shareToWechat">
              <text class="action-icon">
                📤
              </text>
              <text class="action-text">
                分享
              </text>
            </view>
          </view>
        </view>

        <view class="invite-stats">
          <view class="stat-item">
            <text class="stat-value">
              {{ inviteStats.totalCount }}
            </text>
            <text class="stat-label">
              已邀请
            </text>
          </view>
          <view class="stat-item">
            <text class="stat-value">
              {{ inviteStats.activeCount }}
            </text>
            <text class="stat-label">
              活跃用户
            </text>
          </view>
          <view class="stat-item">
            <text class="stat-value">
              {{ inviteStats.teamCount }}
            </text>
            <text class="stat-label">
              团队用户
            </text>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-else-if="loading" class="loading-state">
        <view class="loading-spinner" />
        <text class="loading-text">
          加载中...
        </text>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="error" class="error-state">
        <text class="error-text">
          {{ error }}
        </text>
        <view v-if="!hasTeamPermission" class="upgrade-btn" @tap="goToMembership">
          开通团队权限
        </view>
        <view v-else class="retry-btn" @tap="getMyInviteCode">
          重试
        </view>
      </view>

      <!-- 还未获取 -->
      <view v-else class="empty-state">
        <text class="empty-text">
          尚未获取邀请码
        </text>
        <view class="get-code-btn" @tap="getMyInviteCode">
          获取邀请码
        </view>
      </view>
    </view>

    <!-- 未登录提示 -->
    <view v-else class="login-prompt">
      <text class="prompt-text">
        请先登录账号
      </text>
      <view class="login-btn" @tap="() => uni.navigateTo({ url: '/pages/login/login' })">
        去登录
      </view>
    </view>

    <!-- 接受邀请 -->
    <view class="accept-section">
      <text class="section-title">
        接受邀请
      </text>

      <view v-if="!showAcceptForm" class="accept-intro">
        <text class="intro-text">
          收到好友邀请码？
        </text>
        <view class="show-form-btn" @tap="toggleAcceptForm">
          输入邀请码
        </view>
      </view>

      <view v-else class="accept-form">
        <input
          v-model="inputCode" class="code-input" placeholder="请输入邀请码" maxlength="12"
          :disabled="processing"
        >
        <view class="form-actions">
          <view class="cancel-btn" @tap="toggleAcceptForm">
            取消
          </view>
          <view class="accept-btn" :class="{ processing }" @tap="acceptInviteCode">
            {{ processing ? '处理中...' : '接受邀请' }}
          </view>
        </view>
      </view>
    </view>

    <!-- 邀请说明 -->
    <view class="invite-info">
      <text class="section-title">
        邀请说明
      </text>

      <view class="info-card">
        <view class="info-item">
          <text class="info-icon">
            🎁
          </text>
          <view class="info-content">
            <text class="info-title">
              邀请奖励
            </text>
            <text class="info-desc">
              成功邀请好友加入，双方均可获得额外的AI解读次数
            </text>
          </view>
        </view>

        <view class="info-item">
          <text class="info-icon">
            👥
          </text>
          <view class="info-content">
            <text class="info-title">
              团队权益
            </text>
            <text class="info-desc">
              被邀请者可获得团队权限，享有更多专业服务
            </text>
          </view>
        </view>

        <view class="info-item">
          <text class="info-icon">
            📊
          </text>
          <view class="info-content">
            <text class="info-title">
              数据共享
            </text>
            <text class="info-desc">
              团队成员可共享心理健康报告，建立支持系统
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.invite-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 32rpx;
}

.header-card {
  background: linear-gradient(135deg, #6f42c1, #9370db);
  padding: 40rpx 32rpx;
  border-radius: 24rpx;
  margin-bottom: 40rpx;
}

.header-content {
  color: #fff;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: block;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.invite-code-section,
.accept-section,
.invite-info {
  margin-bottom: 48rpx;
}

.invite-code-card {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.code-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
}

.code-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #6f42c1;
  margin-bottom: 24rpx;
  font-family: monospace;
  letter-spacing: 4rpx;
}

.code-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  background-color: #f0f0f0;
}

.copy-btn.copied {
  background-color: #4caf50;
  color: white;
}

.share-btn {
  background-color: #6f42c1;
  color: white;
}

.action-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.action-text {
  font-size: 24rpx;
}

.invite-stats {
  display: flex;
  justify-content: space-around;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.loading-state {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #6f42c1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.error-state {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.error-text {
  font-size: 28rpx;
  color: #f44336;
  margin-bottom: 24rpx;
  text-align: center;
}

.upgrade-btn,
.retry-btn {
  padding: 20rpx 48rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.upgrade-btn {
  background-color: #6f42c1;
  color: white;
}

.retry-btn {
  background-color: #f0f0f0;
  color: #333;
}

.empty-state {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 24rpx;
}

.get-code-btn {
  background-color: #6f42c1;
  color: white;
  padding: 20rpx 48rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.login-prompt {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.prompt-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 24rpx;
}

.login-btn {
  background-color: #6f42c1;
  color: white;
  padding: 20rpx 48rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.accept-intro {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.intro-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 24rpx;
}

.show-form-btn {
  background-color: #6f42c1;
  color: white;
  padding: 20rpx 48rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.accept-form {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.code-input {
  width: 100%;
  height: 96rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  font-family: monospace;
  text-align: center;
  margin-bottom: 24rpx;
}

.form-actions {
  display: flex;
  gap: 24rpx;
}

.cancel-btn,
.accept-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.accept-btn {
  background-color: #6f42c1;
  color: white;
}

.accept-btn.processing {
  background-color: #b8a2db;
}

.info-card {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.info-item {
  display: flex;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.info-content {
  flex: 1;
}

.info-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.info-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
</style>
