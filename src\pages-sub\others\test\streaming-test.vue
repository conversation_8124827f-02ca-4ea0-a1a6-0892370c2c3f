<script setup lang="ts">
import { ref } from 'vue'
import { ChatAPI } from '@/api/chat'

const isLoading = ref(false)
const error = ref('')
const receivedContent = ref('')
const logs = ref<string[]>([])
const chunkCount = ref(0)
const requestTask = ref<any>(null)

function addLog(message: string) {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.push(`[${timestamp}] ${message}`)
  console.log(message)
}

function clearLogs() {
  logs.value = []
  receivedContent.value = ''
  error.value = ''
  chunkCount.value = 0
}

function cancelRequest() {
  if (requestTask.value) {
    requestTask.value.abort?.()
    addLog('用户取消了请求')
    isLoading.value = false
  }
}

async function testStreaming() {
  isLoading.value = true
  error.value = ''
  receivedContent.value = ''
  logs.value = []
  chunkCount.value = 0

  addLog('开始流式传输测试')

  // 详细的环境检测
  const systemInfo = uni.getSystemInfoSync()
  addLog(`平台: ${systemInfo.platform}`)
  addLog(`版本: ${systemInfo.version}`)
  addLog(`基础库版本: ${systemInfo.SDKVersion}`)
  addLog(`微信版本: ${systemInfo.version}`)

  // 检查流式传输支持
  if (typeof uni.canIUse === 'function') {
    const supportsChunked = uni.canIUse('request.enableChunked')
    const supportsOnChunkReceived = uni.canIUse('requestTask.onChunkReceived')
    addLog(`enableChunked 支持: ${supportsChunked ? '✅' : '❌'}`)
    addLog(`onChunkReceived 支持: ${supportsOnChunkReceived ? '✅' : '❌'}`)
  }
  else {
    addLog('❌ 无法检测流式传输支持（uni.canIUse 不可用）')
  }

  // 检查当前运行环境
  // #ifdef MP-WEIXIN
  addLog('✅ 当前运行在微信小程序环境')
  // #endif

  // #ifdef H5
  addLog('⚠️ 当前运行在H5环境，可能不支持流式传输')
  // #endif

  // #ifdef APP-PLUS
  addLog('⚠️ 当前运行在APP环境，流式传输支持可能有限')
  // #endif

  try {
    const testData = {
      conversationId: '06b3b460-9cff-42d1-929a-bff9ceafb0ca',
      content: '请简单介绍一下人工智能的发展历史，内容丰富一些',
      type: 'text' as const,
      streaming: true,
      metadata: {
        topic: 'streaming-test',
      },
    }

    addLog('发送测试请求...')
    addLog(`请求URL: ${import.meta.env.VITE_SERVER_BASEURL}/coze/messages/stream`)

    const result = await ChatAPI.sendMessageStream(testData, {
      onMessage: (event: string, data: any) => {
        chunkCount.value++
        addLog(`📦 收到事件 #${chunkCount.value}: ${event}`)

        if (event === 'delta' && data?.content) {
          // 增量更新
          receivedContent.value = data.fullContent || (receivedContent.value + data.content)
          addLog(`🔄 内容增量: +${data.content?.length || 0} 字符`)
        }
        else if (event === 'message' && data?.content) {
          // 消息更新
          receivedContent.value = data.content
          addLog(`📝 消息更新: ${data.content?.length || 0} 字符`)
        }
        else if (event === 'completed' || event === 'done') {
          addLog(`✅ 流式传输完成，总共收到 ${chunkCount.value} 个数据块`)
          if (data?.fullContent) {
            receivedContent.value = data.fullContent
          }
        }
        else if (event === 'error') {
          addLog(`❌ 服务端错误: ${data?.message || JSON.stringify(data)}`)
          error.value = data?.message || '服务端错误'
        }
        else {
          addLog(`📋 其他事件数据: ${JSON.stringify(data)}`)
        }
      },
      onError: (err: any) => {
        addLog(`❌ 请求错误: ${err.message || JSON.stringify(err)}`)
        error.value = err.message || '未知错误'
        isLoading.value = false
      },
      onComplete: () => {
        addLog(`🎉 流式传输完成，总计收到 ${chunkCount.value} 个数据块`)
        addLog(`📊 最终内容长度: ${receivedContent.value.length} 字符`)
        isLoading.value = false
      },
    })

    // 保存请求任务引用，用于取消
    requestTask.value = result.requestTask
    addLog('✅ 请求已发送，等待响应...')

    // 等待请求完成
    await result.promise
  }
  catch (err: any) {
    addLog(`💥 请求异常: ${err.message || JSON.stringify(err)}`)
    error.value = err.message || '请求失败'
    isLoading.value = false
  }
}
</script>

<template>
  <view class="streaming-test">
    <view class="header">
      <text class="title">
        微信小程序流式传输测试
      </text>
      <text class="subtitle">
        测试官方 enableChunked + onChunkReceived
      </text>
    </view>

    <view class="test-area">
      <view class="button-group">
        <button class="test-btn" :disabled="isLoading" @click="testStreaming">
          {{ isLoading ? '测试中...' : '开始流式传输测试' }}
        </button>

        <button
          v-if="isLoading"
          class="cancel-btn"
          @click="cancelRequest"
        >
          取消请求
        </button>

        <button
          class="clear-btn"
          :disabled="isLoading"
          @click="clearLogs"
        >
          清空日志
        </button>
      </view>

      <view v-if="error" class="error">
        ❌ 错误: {{ error }}
      </view>

      <view class="stats">
        <text class="stat-item">
          数据块: {{ chunkCount }}
        </text>
        <text class="stat-item">
          内容长度: {{ receivedContent.length }}
        </text>
        <text class="stat-item">
          状态: {{ isLoading ? '接收中' : '已完成' }}
        </text>
      </view>

      <view class="result-area">
        <text class="result-title">
          📄 接收到的内容:
        </text>
        <view class="content">
          {{ receivedContent || '等待接收数据...' }}
        </view>
      </view>

      <view class="log-area">
        <text class="log-title">
          📋 详细日志:
        </text>
        <view class="logs">
          <view v-for="(log, index) in logs" :key="index" class="log-item">
            {{ log }}
          </view>
          <view v-if="logs.length === 0" class="log-empty">
            暂无日志
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.streaming-test {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.test-area {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.test-btn,
.cancel-btn,
.clear-btn {
  width: 100%;
  height: 80rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
}

.test-btn {
  background: #007aff;
  color: white;
}

.test-btn:disabled {
  background: #ccc;
}

.cancel-btn {
  background: #ff3b30;
  color: white;
}

.clear-btn {
  background: #34c759;
  color: white;
}

.clear-btn:disabled {
  background: #ccc;
}

.error {
  color: #ff3b30;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #ffebee;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff3b30;
}

.stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
}

.result-area,
.log-area {
  margin-top: 30rpx;
}

.result-title,
.log-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.content {
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  min-height: 200rpx;
  font-size: 28rpx;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}

.logs {
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.log-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  font-family: monospace;
  line-height: 1.4;
  word-break: break-all;
}

.log-empty {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  font-style: italic;
}
</style>
