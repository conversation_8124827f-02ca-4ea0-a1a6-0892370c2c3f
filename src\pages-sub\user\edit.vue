<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '编辑个人资料',
  },
}
</route>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'
import { onMounted, ref } from 'vue'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'EditProfile',
})

// 用户store
const userStore = useUserStore()

// 表单数据
const formData = ref({
  name: '',
  email: '',
  phoneNumber: '',
  gender: '',
  birthDate: '',
  address: '',
  fullName: '',
})

// 是否在保存中
const isSaving = ref(false)

// 生日是否已锁定
const birthDateLocked = ref(false)

// 初始化表单数据
function initForm() {
  if (userStore.userDetails) {
    formData.value.name = userStore.userDetails.name || ''
    formData.value.email = userStore.userDetails.email || ''
    formData.value.phoneNumber = userStore.userDetails.phoneNumber || ''
    formData.value.gender = userStore.userDetails.gender || ''
    formData.value.birthDate = userStore.userDetails.birthDate || ''
    formData.value.address = userStore.userDetails.address || ''
    formData.value.fullName = userStore.userDetails.fullName || ''
    birthDateLocked.value = userStore.userDetails.birthDateLocked || false
  }
}

// 选择性别
function selectGender(gender: string) {
  formData.value.gender = gender
}

// 选择出生日期
function onDateChange(e: any) {
  formData.value.birthDate = e.detail.value
}

// 保存个人资料
async function saveProfile() {
  if (!validateForm()) {
    return
  }

  try {
    isSaving.value = true

    // TODO: 调用API更新用户信息
    // 这里应该调用一个实际的API来更新用户信息
    // 暂时模拟更新成功
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟更新本地用户信息
    if (userStore.userDetails) {
      const updatedUser = {
        ...userStore.userDetails,
        name: formData.value.name,
        email: formData.value.email,
        phoneNumber: formData.value.phoneNumber,
        gender: formData.value.gender,
        birthDate: formData.value.birthDate,
        address: formData.value.address,
        fullName: formData.value.fullName,
        birthDateLocked: true, // 更新后锁定生日
      }

      // 更新用户信息
      userStore.setUserDetails(updatedUser, userStore.token)

      // 提示成功
      uni.showToast({
        title: '保存成功',
        icon: 'success',
      })

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'error',
    })
  }
  finally {
    isSaving.value = false
  }
}

// 表单验证
function validateForm() {
  // 必填项验证
  if (!formData.value.name) {
    uni.showToast({
      title: '请输入用户名',
      icon: 'none',
    })
    return false
  }

  if (!formData.value.email) {
    uni.showToast({
      title: '请输入邮箱',
      icon: 'none',
    })
    return false
  }

  // 邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/
  if (!emailRegex.test(formData.value.email)) {
    uni.showToast({
      title: '邮箱格式不正确',
      icon: 'none',
    })
    return false
  }

  return true
}

// 页面加载时初始化表单
onLoad(() => {
  if (!userStore.isLoggedIn) {
    uni.showToast({
      title: '请先登录',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/login/login',
      })
    }, 1500)
    return
  }

  initForm()
})

// 返回上一页
function goBack() {
  uni.navigateBack()
}
</script>

<template>
  <view class="edit-profile-container">
    <view class="header">
      <text class="title">
        编辑个人资料
      </text>
      <text class="subtitle">
        完善个人资料，获取更个性化的测评体验
      </text>
    </view>

    <view class="avatar-section">
      <view class="avatar-container">
        <image
          :src="userStore.userDetails?.profilePicture || '/static/images/default-avatar.png'" class="avatar"
          mode="aspectFill"
        />
        <view class="avatar-edit-icon">
          <text class="icon">
            📷
          </text>
        </view>
      </view>
      <text class="avatar-label">
        点击更换头像
      </text>
    </view>

    <view class="form-section">
      <view class="form-group">
        <text class="form-label">
          用户名
        </text>
        <input v-model="formData.name" class="form-input" placeholder="请输入用户名">
      </view>

      <view class="form-group">
        <text class="form-label">
          真实姓名 <text class="optional">
            (选填)
          </text>
        </text>
        <input v-model="formData.fullName" class="form-input" placeholder="请输入真实姓名">
      </view>

      <view class="form-group">
        <text class="form-label">
          邮箱
        </text>
        <input v-model="formData.email" class="form-input" placeholder="请输入邮箱">
      </view>

      <view class="form-group">
        <text class="form-label">
          手机号
        </text>
        <input v-model="formData.phoneNumber" class="form-input" placeholder="请输入手机号" disabled>
        <text class="input-tip">
          手机号为登录账号，不可修改
        </text>
      </view>

      <view class="form-group">
        <text class="form-label">
          性别
        </text>
        <view class="gender-options">
          <view class="gender-option" :class="{ active: formData.gender === '男' }" @tap="selectGender('男')">
            <text class="gender-icon">
              👨
            </text>
            <text class="gender-text">
              男
            </text>
          </view>
          <view class="gender-option" :class="{ active: formData.gender === '女' }" @tap="selectGender('女')">
            <text class="gender-icon">
              👩
            </text>
            <text class="gender-text">
              女
            </text>
          </view>
        </view>
      </view>

      <view class="form-group">
        <text class="form-label">
          出生日期 <text v-if="birthDateLocked" class="locked">
            (已锁定)
          </text>
        </text>
        <picker
          mode="date" :value="formData.birthDate" :disabled="birthDateLocked"
          :end="new Date().toISOString().split('T')[0]" @change="onDateChange"
        >
          <view class="form-input date-picker">
            {{ formData.birthDate || '请选择出生日期' }}
            <text v-if="!birthDateLocked" class="picker-icon">
              ▼
            </text>
          </view>
        </picker>
        <text v-if="!birthDateLocked" class="input-tip">
          注意: 出生日期提交后将锁定，无法修改
        </text>
      </view>

      <view class="form-group">
        <text class="form-label">
          地址 <text class="optional">
            (选填)
          </text>
        </text>
        <input v-model="formData.address" class="form-input" placeholder="请输入地址">
      </view>

      <view class="action-buttons">
        <button class="cancel-btn" @tap="goBack()">
          取消
        </button>
        <button class="save-btn" :disabled="isSaving" @tap="saveProfile">
          {{ isSaving ? '保存中...' : '保存' }}
        </button>
      </view>
    </view>
  </view>
</template>

<style scoped>
.edit-profile-container {
  padding: 32rpx;
  background-color: #f8f8fc;
  min-height: 100vh;
}

.header {
  margin-bottom: 48rpx;
  text-align: center;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  display: block;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #555555;
  display: block;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 48rpx;
}

.avatar-container {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 16rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.avatar-edit-icon {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 60rpx;
  height: 60rpx;
  background-color: #6a5acd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid #fff;
}

.icon {
  font-size: 28rpx;
  color: #fff;
}

.avatar-label {
  font-size: 24rpx;
  color: #555555;
}

.form-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.optional,
.locked {
  font-size: 24rpx;
  font-weight: normal;
  color: #999999;
}

.locked {
  color: #ff9800;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background-color: #f0f8ff;
  border: 1rpx solid #e0e0e0;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333333;
}

.input-tip {
  font-size: 24rpx;
  color: #999999;
  margin-top: 12rpx;
  display: block;
}

.gender-options {
  display: flex;
  gap: 32rpx;
}

.gender-option {
  flex: 1;
  height: 100rpx;
  background-color: #f0f8ff;
  border: 1rpx solid #e0e0e0;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.gender-option.active {
  background-color: #6a5acd;
  border-color: #6a5acd;
}

.gender-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.gender-text {
  font-size: 28rpx;
  color: #333333;
}

.gender-option.active .gender-text {
  color: #ffffff;
}

.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333333;
}

.date-picker:empty {
  color: #999999;
}

.picker-icon {
  font-size: 24rpx;
  color: #999999;
}

.action-buttons {
  display: flex;
  gap: 32rpx;
  margin-top: 48rpx;
}

.cancel-btn,
.save-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #555555;
  border: 1rpx solid #e0e0e0;
}

.save-btn {
  background: linear-gradient(135deg, #6a5acd, #9370db);
  color: #ffffff;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(106, 90, 205, 0.3);
}

.save-btn:disabled {
  background: linear-gradient(135deg, #b8a2db, #c0b6db);
  box-shadow: none;
}
</style>
