<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
  },
}
</route>

<script lang="ts" setup>
//

function getP() {
  return new Promise((resolve, reject) => {
    uni.request({
      url: 'https://testpay.klzai.com/pay',
      data: {
        openid: 'opI5Gvk8hYIsF_owssSQ_2PvVu6c',
      },
      header: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
      },
      method: 'GET',
      sslVerify: true,
      success: ({ data }) => {
        resolve(data)
      },
      fail: (error) => {
        reject(error)
      },
    })
  })
}

async function testWechatPay() {
  try {
    const getParms = await getP()
    const { appId, nonceStr, package: pkg, paySign, signType, timeStamp } = getParms.data
    uni.requestPayment({
      provider: 'wxpay',
      appId,
      nonceStr,
      package: pkg,
      paySign,
      signType,
      timeStamp,
      orderInfo: {
        appId,
        nonceStr,
        package: pkg,
        paySign,
        signType,
        timeStamp,
      },
      success: (res) => {
        console.log('success', res)
      },
      fail: (err) => {
        console.log('fail', err)
      },
    })
  }
  catch (error) {
    console.log(error)
  }
}
</script>

<template>
  <button @tap="testWechatPay()">
    测试微信支付
  </button>
</template>

  <style lang="scss" scoped>
  //
</style>
