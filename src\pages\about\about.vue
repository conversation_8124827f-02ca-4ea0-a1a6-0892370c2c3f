<route lang="json5">
{
  layout: 'tabbar',
  style: {
    navigationBarTitleText: '关于',
  },
}
</route>

<script lang="ts" setup>
import RequestComp from './components/request.vue'
import UploadComp from './components/upload.vue'

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 奇怪：同样的代码放在 vue 里面不会校验到错误，放在 .ts 文件里面会校验到错误
// const testOxlint = (name: string) => {
//   console.log('oxlint')
// }
// testOxlint('oxlint')
console.log('about')

function gotoAlova() {
  uni.navigateTo({
    url: '/pages/about/alova',
  })
}

function gotoChatTest() {
  uni.navigateTo({
    url: '/pages/about/chat-test',
  })
}

function gotoSessionTest() {
  uni.navigateTo({
    url: '/pages/about/session-test',
  })
}
</script>

<template>
  <view>
    <view class="mt-8 text-center text-3xl">
      鸽友们好，我是
      <text class="text-red-500">
        菲鸽
      </text>
    </view>
    <RequestComp />
    <UploadComp />
    <view class="test-links">
      <button class="test-btn alova" @click="gotoAlova">
        前往 alova 页面
      </button>
      <button class="test-btn chat" @click="gotoChatTest">
        聊天功能测试
      </button>
      <button class="test-btn session" @click="gotoSessionTest">
        会话管理测试
      </button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.test-css {
  // 16rpx=>0.5rem
  padding-bottom: 16rpx;
  // mt-4=>1rem=>16px;
  margin-top: 16px;
  text-align: center;
}

.test-links {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 32rpx;
}

.test-btn {
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.test-btn:active {
  transform: scale(0.98);
}

.test-btn.alova {
  background-color: #17a2b8;
  color: #ffffff;
}

.test-btn.chat {
  background-color: #28a745;
  color: #ffffff;
}

.test-btn.session {
  background-color: #ffc107;
  color: #333333;
}
</style>
