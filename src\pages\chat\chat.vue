<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: 'AI助手',
  },
}
</route>

<script lang="ts" setup>
import type { LocalMessage } from '@/composables/use-real-chat'
import { onLoad } from '@dcloudio/uni-app'
import { computed, nextTick, onMounted, onUnmounted, ref, watch, watchEffect } from 'vue'
import ChatForm from '@/components/ChatForm/ChatForm.vue'
import { useRealChat } from '@/composables/use-real-chat'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'ChatPage',
})

// 用户store
const userStore = useUserStore()

// 使用真实的聊天功能
const {
  sessions,
  currentSessionId,
  messageList,
  isLoading,
  isAITyping,
  error,
  isStreamingEnabled, // 流式传输开关状态
  currentStreamingMessageId, // 当前流式消息ID
  currentStreamingRequest, // 当前流式请求状态
  streamingChunkCount, // 新增：流式传输数据块计数
  initializeSessions,
  createNewSession,
  sendMessage: sendChatMessage,
  switchSession,
  deleteSession,
  refreshSessions,
  clearError,
  toggleStreamingMode, // 切换流式传输模式的方法
  clearStreamingTimers, // 清理定时器的方法
  cancelStreamingMessage, // 取消流式传输的方法
} = useRealChat()

// 组件卸载时清理定时器
onUnmounted(() => {
  clearStreamingTimers()
})

// 本地状态
const currentInput = ref('')
const pageType = ref<'basic' | 'daily' | 'current'>('basic') // 页面类型：basic, daily, current
const showForm = ref(false) // 是否显示表单
const formTitle = ref('') // 表单标题
const showPopup = ref(true) // 是否显示弹出层

// 会话管理状态
const showSessionList = ref(false) // 是否显示会话列表
const showSessionMenu = ref(false) // 是否显示会话菜单
const selectedSessionId = ref('') // 选中的会话ID（用于删除确认）

// 表单引用
const chatFormRef = ref()

// 获取屏幕边界到安全区域距离
let safeAreaInsets
let systemInfo

// #ifdef MP-WEIXIN
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif

// 计算显示的页面标题
const pageTitle = computed(() => {
  switch (pageType.value) {
    case 'basic':
      return '基础测评'
    case 'daily':
      return '今日提醒'
    case 'current':
      return '当下状态'
    default:
      return 'AI助手'
  }
})

// 处理表单提交
async function handleFormSubmit(formData: any) {
  // 根据不同的表单类型，生成不同的消息
  let message = ''

  if (pageType.value === 'basic') {
    message = `您本次输入的信息是：\n\n问题：${formData.question}\n姓名：${formData.name}\n性别：${formData.gender}\n原型：${formData.archetype}\n第一张卡牌：${formData.firstCard}\n第二张卡牌：${formData.secondCard}\n对方原型：${formData.oppositeArchetype}\n\n人工智能体开始解析数据.. ... 整个过程可能需要30秒`
  }
  else if (pageType.value === 'daily') {
    message = `您本次输入的信息是：\n\n姓名：${formData.name}\n性别：${formData.gender}\n今日主题：${formData.theme}\n当前状态：${formData.currentStatus}\n第一张卡牌：${formData.firstCard}\n第二张卡牌：${formData.secondCard}\n\n人工智能体开始解析今日提醒数据.. ... 整个过程可能需要30秒`
  }
  else if (pageType.value === 'current') {
    message = `您本次输入的信息是：\n\n姓名：${formData.name}\n性别：${formData.gender}\n当下主题：${formData.theme}\n当前状态：${formData.currentStatus}\n第一张卡牌：${formData.firstCard}\n\n人工智能体开始解析当下状态.. ... 整个过程可能需要30秒`
  }

  // 隐藏表单，显示聊天界面
  showForm.value = false
  showPopup.value = false

  // 发送消息并等待完成
  try {
    await sendChatMessage(message)

    // 消息发送完成后滚动到底部
    await nextTick()
    scrollToBottom()
  }
  catch (error) {
    console.error('发送消息失败:', error)
  }
}

// 根据页面类型设置表单
function setupFormByType(type) {
  pageType.value = type || 'basic'

  switch (pageType.value) {
    case 'basic':
      formTitle.value = '基础测评'
      break
    case 'daily':
      formTitle.value = '今日提醒'
      break
    case 'current':
      formTitle.value = '当下状态'
      break
    default:
      formTitle.value = '基础测评'
      pageType.value = 'basic'
  }
}

// 发送消息
async function sendMessage() {
  if (!currentInput.value.trim() || isAITyping.value) {
    return
  }

  const messageContent = currentInput.value.trim()
  currentInput.value = ''
  await sendChatMessage(messageContent)
  await nextTick()
  scrollToBottom()
}

// 创建新会话的包装函数
async function handleCreateNewSession() {
  await createNewSession()
  // 创建后滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

// 切换会话
function handleSwitchSession(sessionId: string) {
  switchSession(sessionId)
  showSessionList.value = false
}

// 显示会话菜单
function showSessionOptions(sessionId: string) {
  selectedSessionId.value = sessionId
  showSessionMenu.value = true
}

// 删除会话
async function handleDeleteSession() {
  if (!selectedSessionId.value)
    return

  try {
    await deleteSession(selectedSessionId.value)
    showSessionMenu.value = false
    selectedSessionId.value = ''

    uni.showToast({
      title: '会话已删除',
      icon: 'success',
      duration: 1500,
    })
  }
  catch (error) {
    console.error('删除会话失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error',
      duration: 2000,
    })
  }
}

// 刷新会话列表
async function handleRefreshSessions() {
  try {
    await refreshSessions()
    uni.showToast({
      title: '已刷新',
      icon: 'success',
      duration: 1000,
    })
  }
  catch (error) {
    console.error('刷新会话列表失败:', error)
  }
}

// 格式化时间
function formatTime(timestamp: number) {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  // 今天
  if (diff < 24 * 60 * 60 * 1000) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }
  // 昨天
  else if (diff < 48 * 60 * 60 * 1000) {
    return '昨天'
  }
  // 其他日期
  else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

// 获取会话预览内容
function getSessionPreview(session: any) {
  if (!session.messages || session.messages.length === 0) {
    return '暂无消息'
  }

  const lastMessage = session.messages[session.messages.length - 1]
  const content = lastMessage.content || ''
  return content.length > 30
    ? `${content.substring(0, 30)}...`
    : content
}

// 获取会话最后活跃时间
function getSessionTime(session: any) {
  // 使用 lastActiveAt 或 createTime 作为显示时间
  const timeToUse = session.lastActiveAt || session.createTime
  return formatTime(timeToUse)
}

function handleBack() {
  uni.navigateBack()
}

// 滚动到底部
function scrollToBottom() {
  const query = uni.createSelectorQuery()
  query.select('.message-list').boundingClientRect()
  query.exec((res) => {
    if (res[0]) {
      uni.pageScrollTo({
        scrollTop: res[0].height,
        duration: 300,
      })
    }
  })
}

// 处理错误显示
function handleError() {
  if (error.value) {
    uni.showToast({
      title: error.value,
      icon: 'error',
      duration: 2000,
      complete: () => {
        clearError()
      },
    })
  }
}

// 显示环境信息
function showEnvironmentInfo() {
  const systemInfo = uni.getSystemInfoSync()
  let envInfo = ''

  // #ifdef MP-WEIXIN
  envInfo = `微信小程序环境\n基础库版本: ${systemInfo.SDKVersion}\n微信版本: ${systemInfo.version}\n平台: ${systemInfo.platform}`

  // 检查流式传输支持
  if (typeof uni.canIUse === 'function') {
    const supportsChunked = uni.canIUse('request.enableChunked')
    const supportsOnChunkReceived = uni.canIUse('requestTask.onChunkReceived')
    envInfo += `\n流式传输支持: ${supportsChunked && supportsOnChunkReceived ? '✅ 支持' : '❌ 不支持'}`
  }
  // #endif

  // #ifdef H5
  envInfo = `H5环境\n浏览器: ${systemInfo.browserName}\n版本: ${systemInfo.browserVersion}\n平台: ${systemInfo.platform}\n⚠️ 不支持真正的流式传输`
  // #endif

  // #ifdef APP-PLUS
  envInfo = `APP环境\n平台: ${systemInfo.platform}\n版本: ${systemInfo.osVersion}\n⚠️ 流式传输支持有限`
  // #endif

  uni.showModal({
    title: '运行环境信息',
    content: envInfo,
    showCancel: false,
    confirmText: '确定',
  })
}

// 快速测试流式传输
async function testStreaming() {
  const testMessage = '您本次输入的信息是： 问题：无意识状态 姓名：公子知 性别：男 原型：52 第一张卡牌：1 第二张卡牌：64 对方原型：0 人工智能体开始解析数据.. ... 整个过程可能需要30秒'
  await sendChatMessage(testMessage)
  await nextTick()
  scrollToBottom()
}

// 从路由参数获取页面类型
onLoad((options) => {
  setupFormByType(options.type)
})

function handleClose() {
  showPopup.value = false
  console.log('弹出层已关闭')
}

// 监听消息列表变化
watchEffect(() => {
  if (messageList.value.length > 0) {
    const lastMessage = messageList.value[messageList.value.length - 1]
  }
})

// 页面加载时初始化
onMounted(async () => {
  uni.showLoading({
    title: '加载中...',
  })

  try {
    await initializeSessions()
  }
  catch (err) {
    console.error('初始化失败:', err)
  }
  finally {
    uni.hideLoading()
  }

  // 监听错误状态
  if (error.value) {
    handleError()
  }
})
</script>

<template>
  <view class="chat-container">
    <!-- 顶部导航栏 -->
    <wd-navbar
      :title="pageTitle" left-text="返回" left-arrow placeholder fixed :safe-area-inset-top="true"
      @click-left="handleBack"
    >
      <template #right>
        <view class="header-actions">
          <!-- 环境指示器 -->
          <view class="env-indicator" @tap="showEnvironmentInfo">
            <!-- #ifdef MP-WEIXIN -->
            <text class="env-icon">
              🔄
            </text>
            <text class="env-text">
              微信
            </text>
            <!-- #endif -->
            <!-- #ifdef H5 -->
            <text class="env-icon">
              🌐
            </text>
            <text class="env-text">
              H5
            </text>
            <!-- #endif -->
            <!-- #ifdef APP-PLUS -->
            <text class="env-icon">
              📱
            </text>
            <text class="env-text">
              APP
            </text>
            <!-- #endif -->
          </view>

          <view class="streaming-switch" @tap="toggleStreamingMode">
            <text class="switch-icon" :class="{ active: isStreamingEnabled }">
              {{ isStreamingEnabled ? '⚡' : '💬' }}
            </text>
            <text class="switch-text">
              {{ isStreamingEnabled ? '流式' : '标准' }}
            </text>
          </view>
        </view>
      </template>
    </wd-navbar>
    <!-- <view class="chat-header">
      <view class="header-left">
        <text class="back-icon" @tap="handleBack">
          ←
        </text>
        <text class="header-title">
          {{ pageTitle }}
        </text>
      </view>
    </view> -->

    <!-- 会话列表侧边栏 -->
    <wd-popup v-model="showSessionList" position="left" :style="{ width: '80%', height: '100%' }" class="absolute">
      <view class="session-list-container h-full w-full" :style="{ marginTop: `${safeAreaInsets?.top * 2}px` }">
        <view class="session-list-header">
          <text class="session-list-title">
            会话列表
          </text>
          <view class="session-list-actions">
            <view class="action-btn" @tap="handleRefreshSessions">
              <text class="action-btn-icon">
                🔄
              </text>
            </view>
            <view class="action-btn" @tap="showSessionList = false">
              <text class="action-btn-icon">
                ✕
              </text>
            </view>
          </view>
        </view>

        <view class="session-list">
          <view
            v-for="session in sessions" :key="session.id" class="session-item"
            :class="{ active: session.id === currentSessionId }" @tap="handleSwitchSession(session.id)"
            @longpress="showSessionOptions(session.id)"
          >
            <view class="session-info">
              <text class="session-title">
                {{ session.title || '新的对话' }}
              </text>
              <text class="session-preview">
                {{ getSessionPreview(session) }}
              </text>
              <text class="session-time">
                {{ getSessionTime(session) }}
              </text>
            </view>
            <view class="session-actions">
              <view class="session-action-btn" @tap.stop="showSessionOptions(session.id)">
                <text class="session-action-icon">
                  ⋯
                </text>
              </view>
            </view>
          </view>

          <view v-if="sessions.length === 0" class="empty-sessions">
            <text class="empty-text">
              暂无会话
            </text>
            <view class="empty-btn" @tap="handleCreateNewSession">
              <text class="empty-btn-text">
                创建新会话
              </text>
            </view>
          </view>
        </view>
        <!-- 底部操作区域 -->
        <view class="bottom-actions">
          <view class="action-bar">
            <view class="session-btn" @tap="showSessionList = true">
              <text class="session-btn-icon">
                📋
              </text>
              <text class="session-btn-text">
                会话
              </text>
            </view>
            <view class="action-divider" />
            <view class="new-session-btn" @tap="handleCreateNewSession">
              <text class="new-session-btn-icon">
                ➕
              </text>
              <text class="new-session-btn-text">
                新对话
              </text>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 会话操作菜单 -->
    <wd-popup v-model="showSessionMenu" position="bottom" :style="{ height: '200rpx' }" class="absolute">
      <view class="session-menu">
        <view class="menu-item delete" @tap="handleDeleteSession">
          <text class="menu-icon">
            🗑️
          </text>
          <text class="menu-text">
            删除会话
          </text>
        </view>
        <view class="menu-item cancel" @tap="showSessionMenu = false">
          <text class="menu-text">
            取消
          </text>
        </view>
      </view>
    </wd-popup>

    <!-- 弹出层 -->
    <wd-popup
      v-model="showPopup" position="bottom" closable
      custom-style="padding-top: 20rpx; border-radius: 16px 16px 0 0;" @close="handleClose"
    >
      <ChatForm ref="chatFormRef" :page-type="pageType" @submit="handleFormSubmit" />
    </wd-popup>

    <!-- 聊天界面 -->
    <view v-if="!showPopup" class="chat-main">
      <view class="messages-container">
        <!-- 加载状态 -->
        <view v-if="isLoading && messageList.length === 0" class="loading-container">
          <view class="loading-spinner" />
          <text class="loading-text">
            正在加载会话...
          </text>
        </view>

        <!-- 错误状态 -->
        <view v-if="error && messageList.length === 0" class="error-container">
          <text class="error-text">
            {{ error }}
          </text>
          <view class="retry-btn" @tap="initializeSessions">
            <text class="retry-text">
              重试
            </text>
          </view>
        </view>

        <view v-for="message in messageList" :key="message.id" class="message-item" :class="message.type">
          <!-- AI消息 -->
          <view v-if="message.type === 'ai'" class="ai-message">
            <view class="ai-avatar">
              <text class="avatar-icon">
                🤖
              </text>
            </view>
            <view class="message-content ai-content">
              <view v-if="message.isLoading" class="loading-content">
                <view class="typing-indicator">
                  <view class="dot" />
                  <view class="dot" />
                  <view class="dot" />
                </view>
                <text class="loading-text">
                  AI正在思考中...
                </text>
              </view>
              <view v-else-if="message.isStreaming" class="streaming-content">
                <text class="message-text">
                  {{ message.content }}
                </text>
                <view class="streaming-indicator">
                  <view class="streaming-main-info">
                    <text class="streaming-icon">
                      ⚡
                    </text>
                    <view class="streaming-info">
                      <text class="streaming-text">
                        微信流式传输中...
                      </text>
                      <text v-if="streamingChunkCount > 0" class="streaming-stats">
                        已接收 {{ streamingChunkCount }} 个数据块
                      </text>
                    </view>
                  </view>
                  <view
                    v-if="currentStreamingRequest && message.id === currentStreamingMessageId"
                    class="cancel-streaming-btn" @tap="cancelStreamingMessage"
                  >
                    <text class="cancel-icon">
                      ✕
                    </text>
                  </view>
                </view>
              </view>
              <text v-else class="message-text">
                {{ message.content }}
              </text>
            </view>
          </view>

          <!-- 用户消息 -->
          <view v-else class="user-message">
            <view class="message-content user-content">
              <text class="message-text">
                {{ message.content }}
              </text>
            </view>
            <view class="user-avatar">
              <text class="avatar-icon">
                👤
              </text>
            </view>
          </view>
        </view>
        <!-- 输入区域 -->
      </view>
    </view>

    <view class="input-area">
      <view class="input-container">
        <view class="session-btn" @tap="showSessionList = true">
          <text class="session-btn-icon">
            📋
          </text>
          <text class="session-btn-text">
            会话
          </text>
        </view>
        <input
          v-model="currentInput" class="message-input" placeholder="输入您的问题或感受..." :disabled="isAITyping"
          confirm-type="send" @confirm="sendMessage"
        >
        <!-- 快速测试按钮 -->
        <view v-if="isStreamingEnabled && !currentInput.trim()" class="test-streaming-btn" @tap="testStreaming">
          <text class="test-icon">
            ⚡
          </text>
        </view>
        <view class="send-btn" :class="{ disabled: !currentInput.trim() || isAITyping }" @tap="sendMessage">
          <text class="send-icon">
            →
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
text {
  user-select: text;
}

/* 聊天容器 */
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏相关样式 */
.header-actions {
  display: flex;
  align-items: center;
  padding-right: 16rpx;
}

.env-indicator {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.env-indicator:active {
  transform: scale(0.95);
  background-color: #e9ecef;
}

.env-icon {
  font-size: 28rpx;
  color: #666666;
  margin-right: 2rpx;
  transition: all 0.3s ease;
}

.env-text {
  font-size: 20rpx;
  color: #666666;
  font-weight: 500;
  transition: all 0.3s ease;
}

.env-indicator:active .env-text {
  color: #007aff;
}

.streaming-switch {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.streaming-switch:active {
  transform: scale(0.95);
  background-color: #e9ecef;
}

.switch-icon {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 2rpx;
  transition: all 0.3s ease;
}

.switch-icon.active {
  color: #007aff;
}

.switch-text {
  font-size: 20rpx;
  color: #666666;
  font-weight: 500;
  transition: all 0.3s ease;
}

.streaming-switch:active .switch-text {
  color: #007aff;
}

/* 流式传输状态指示 */
.streaming-content {
  position: relative;
}

.streaming-indicator {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  padding: 4rpx 8rpx;
  background-color: #e3f2fd;
  border-radius: 8rpx;
  border: 1rpx solid #bbdefb;
}

.streaming-main-info {
  display: flex;
  align-items: center;
}

.streaming-icon {
  font-size: 24rpx;
  color: #007aff;
  margin-right: 6rpx;
  animation: pulse 1.5s infinite;
}

.streaming-info {
  display: flex;
  flex-direction: column;
}

.streaming-text {
  font-size: 20rpx;
  color: #007aff;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.streaming-stats {
  font-size: 20rpx;
  color: #666666;
}

.cancel-streaming-btn {
  margin-left: 8rpx;
  padding: 2rpx 6rpx;
  background-color: #ff4444;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24rpx;
  height: 24rpx;
  transition: all 0.3s ease;
}

.cancel-streaming-btn:active {
  transform: scale(0.9);
  background-color: #cc0000;
}

.cancel-icon {
  font-size: 18rpx;
  color: #ffffff;
  font-weight: bold;
  line-height: 1;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

/* 顶部导航栏 */
.chat-header {
  background-color: #ffffff;
  padding: 32rpx 32rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 44rpx;
  color: #333333;
  margin-right: 16rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.header-btn {
  width: 72rpx;
  height: 72rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.header-btn:active {
  transform: scale(0.9);
  background-color: #e0e0e0;
}

.header-btn-icon {
  font-size: 32rpx;
  color: #333333;
}

/* 会话列表样式 */
.session-list-container {
  height: 88%;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.session-list-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.session-list-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.session-list-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
  background-color: #e0e0e0;
}

.action-btn-icon {
  font-size: 28rpx;
  color: #333333;
}

.session-list {
  flex: 1;
  overflow-y: auto;
  padding: 16rpx;
}

.session-item {
  padding: 24rpx;
  margin-bottom: 16rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.session-item.active {
  background-color: #e3f2fd;
  border-color: #007aff;
}

.session-item:active {
  transform: scale(0.98);
  background-color: #e0e0e0;
}

.session-info {
  flex: 1;
  margin-right: 16rpx;
}

.session-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.session-preview {
  font-size: 28rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.session-time {
  font-size: 24rpx;
  color: #999999;
  display: block;
}

.session-actions {
  display: flex;
  align-items: center;
}

.session-action-btn {
  width: 48rpx;
  height: 48rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.session-action-btn:active {
  transform: scale(0.9);
  background-color: #e0e0e0;
}

.session-action-icon {
  font-size: 24rpx;
  color: #666666;
}

.empty-sessions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  min-height: 400rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 32rpx;
}

.empty-btn {
  background-color: #007aff;
  padding: 24rpx 48rpx;
  border-radius: 48rpx;
  transition: all 0.3s ease;
}

.empty-btn:active {
  transform: scale(0.95);
  background-color: #0056b3;
}

.empty-btn-text {
  font-size: 28rpx;
  color: #ffffff;
}

/* 会话菜单样式 */
.session-menu {
  padding: 32rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.menu-item.delete {
  background-color: #ffebee;
  color: #d32f2f;
}

.menu-item.cancel {
  background-color: #f0f0f0;
  color: #666666;
}

.menu-item:active {
  transform: scale(0.98);
}

.menu-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.menu-text {
  font-size: 32rpx;
  font-weight: 500;
}

/* 表单容器 */
.form-container {
  flex: 1;
  overflow-y: auto;
}

.chat-main {
  max-height: 75%;
  overflow: hidden;
  padding-left: 16rpx;
  padding-right: 16rpx;
}

.messages-container {
  height: 100%;
  overflow: auto;
}

/* 返回表单按钮 */
.back-to-form {
  margin: 24rpx 32rpx;
  padding: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
}

.back-to-form:active {
  background-color: #e0e0e0;
}

/* 消息列表 */
.message-list {
  flex: 1;
  padding: 0 32rpx;
}

.message-item {
  margin-bottom: 40rpx;
}

/* AI消息样式 */
.ai-message {
  display: flex;
  align-items: flex-start;
}

.ai-avatar {
  width: 80rpx;
  height: 80rpx;
  background-color: #007aff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar-icon {
  font-size: 32rpx;
  color: #ffffff;
}

.ai-content {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 24rpx 8rpx;
  max-width: 70%;
}

/* 用户消息样式 */
.user-message {
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}

.user-content {
  background-color: #007aff;
  border-radius: 24rpx 24rpx 8rpx 24rpx;
  max-width: 70%;
  margin-right: 24rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  background-color: #666666;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.user-avatar .avatar-icon {
  color: #ffffff;
}

/* 消息内容 */
.message-content {
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.message-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333333;
}

.user-content .message-text {
  color: #ffffff;
}

/* 加载状态 */
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.typing-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #007aff;
  border-radius: 50%;
  margin: 0 4rpx;
  animation: typing 1.4s infinite ease-in-out;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.input-area {
  background-color: #ffffff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
  position: fixed;
  bottom: 0rpx;
  left: 0;
  right: 0;
}

.input-container {
  display: flex;
  align-items: center;
  border-radius: 60rpx;
  padding: 8rpx;
}

.message-input {
  flex: 1;
  padding: 24rpx 32rpx;
  font-size: 32rpx;
  background-color: transparent;
  border: none;
  outline: none;
}

.send-btn {
  width: 88rpx;
  height: 88rpx;
  background-color: #007aff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.send-btn:not(.disabled):active {
  transform: scale(0.9);
  background-color: #0056b3;
}

.send-btn.disabled {
  background-color: #cccccc;
  opacity: 0.6;
}

.send-icon {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}

/* 快速测试按钮样式 */
.test-streaming-btn {
  width: 88rpx;
  height: 88rpx;
  background-color: #4caf50;
  /* 绿色按钮 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  /* 与输入框的距离 */
  transition: all 0.3s ease;
}

.test-streaming-btn:active {
  transform: scale(0.9);
  background-color: #388e3c;
  /* 深绿色 */
}

.test-icon {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}

/* 加载和错误状态样式 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  min-height: 400rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 32rpx;
  color: #666666;
  text-align: center;
}

.error-text {
  font-size: 32rpx;
  color: #ff4444;
  text-align: center;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.retry-btn {
  background-color: #007aff;
  padding: 24rpx 48rpx;
  border-radius: 48rpx;
  transition: all 0.3s ease;
}

.retry-btn:active {
  transform: scale(0.95);
  background-color: #0056b3;
}

.retry-text {
  font-size: 28rpx;
  color: #ffffff;
}

/* 底部操作区域 */
.bottom-actions {
  background-color: #ffffff;
  padding: 16rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
  z-index: 10;
}

.action-bar {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 8rpx 0;
}

.session-btn,
.new-session-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  border: 2rpx solid #e9ecef;
}

.session-btn:active,
.new-session-btn:active {
  transform: scale(0.95);
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.session-btn-icon,
.new-session-btn-icon {
  font-size: 36rpx;
  color: #495057;
  margin-bottom: 4rpx;
}

.session-btn-text,
.new-session-btn-text {
  font-size: 20rpx;
  color: #6c757d;
  font-weight: 500;
}

.action-divider {
  width: 1rpx;
  height: 40rpx;
  background-color: #dee2e6;
}
</style>
