<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: 'AI解读',
      },
    }
    </route>

<script lang="ts" setup>
import type { LocalMessage } from '@/composables/use-real-chat'
import { onLoad } from '@dcloudio/uni-app'
import { computed, nextTick, onMounted, onUnmounted, ref, watch, watchEffect } from 'vue'
import { useRealChat } from '@/composables/use-real-chat'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'InterpretationPage',
})

// 用户store
const userStore = useUserStore()

// 使用真实的聊天功能
const {
  sessions,
  currentSessionId,
  messageList,
  isLoading,
  isAITyping,
  error,
  isStreamingEnabled,
  currentStreamingMessageId,
  currentStreamingRequest,
  streamingChunkCount,
  initializeSessions,
  createNewSession,
  sendMessage: sendChatMessage,
  switchSession,
  deleteSession,
  refreshSessions,
  clearError,
  toggleStreamingMode,
  clearStreamingTimers,
  cancelStreamingMessage,
} = useRealChat()

// 组件卸载时清理定时器
onUnmounted(() => {
  clearStreamingTimers()
})

// 本地状态
const currentInput = ref('')
const showSessionList = ref(false)
const showSessionMenu = ref(false)
const selectedSessionId = ref('')

// 快速提问选项
const quickQuestions = ref([
  '请解读我的原型特征',
  '分析我的性格优势',
  '如何提升自我认知',
  '原型与职业匹配度',
  '原型关系分析',
])

// 格式化时间
function formatTime(timestamp: number) {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 24 * 60 * 60 * 1000) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }
  else if (diff < 48 * 60 * 60 * 1000) {
    return '昨天'
  }
  else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

// 发送消息
async function sendMessage() {
  if (!currentInput.value.trim() || isAITyping.value) {
    return
  }

  const messageContent = currentInput.value.trim()
  currentInput.value = ''
  await sendChatMessage(messageContent)
  await nextTick()
  scrollToBottom()
}

// 快速提问
async function quickQuestion(question: string) {
  if (isAITyping.value)
    return

  await sendChatMessage(question)
  await nextTick()
  scrollToBottom()
}

// 创建新会话
async function handleCreateNewSession() {
  await createNewSession()
  nextTick(() => {
    scrollToBottom()
  })
}

// 切换会话
function handleSwitchSession(sessionId: string) {
  switchSession(sessionId)
  showSessionList.value = false
}

// 显示会话菜单
function showSessionOptions(sessionId: string) {
  selectedSessionId.value = sessionId
  showSessionMenu.value = true
}

// 删除会话
async function handleDeleteSession() {
  if (!selectedSessionId.value)
    return

  try {
    await deleteSession(selectedSessionId.value)
    showSessionMenu.value = false
    selectedSessionId.value = ''

    uni.showToast({
      title: '会话已删除',
      icon: 'success',
      duration: 1500,
    })
  }
  catch (error) {
    console.error('删除会话失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error',
      duration: 2000,
    })
  }
}

// 刷新会话列表
async function handleRefreshSessions() {
  try {
    await refreshSessions()
    uni.showToast({
      title: '已刷新',
      icon: 'success',
      duration: 1000,
    })
  }
  catch (error) {
    console.error('刷新会话列表失败:', error)
  }
}

// 滚动到底部
function scrollToBottom() {
  const query = uni.createSelectorQuery()
  query.select('.interpretation-page').boundingClientRect()
  query.exec((res) => {
    if (res[0]) {
      uni.pageScrollTo({
        scrollTop: res[0].height,
        duration: 300,
      })
    }
  })
}

// 处理错误显示
function handleError() {
  if (error.value) {
    uni.showToast({
      title: error.value,
      icon: 'error',
      duration: 2000,
      complete: () => {
        clearError()
      },
    })
  }
}

// 页面加载时初始化
onMounted(async () => {
  uni.showLoading({
    title: '加载中...',
  })

  try {
    await initializeSessions()
  }
  catch (err) {
    console.error('初始化失败:', err)
  }
  finally {
    uni.hideLoading()
  }

  if (error.value) {
    handleError()
  }
})

// 监听消息列表变化
watchEffect(() => {
  if (messageList.value.length > 0) {
    const lastMessage = messageList.value[messageList.value.length - 1]
  }
})
</script>

<template>
  <view class="interpretation-page" style="height: 100vh; display: flex; flex-direction: column;">
    <!-- 会话列表侧边栏 -->
    <wd-popup v-model="showSessionList" position="left" :style="{ width: '80%', height: '100%' }" class="absolute">
      <view class="session-list-container h-full w-full">
        <view class="session-list-header">
          <text class="session-list-title">
            解读会话
          </text>
          <view class="session-list-actions">
            <view class="action-btn" @tap="handleRefreshSessions">
              <text class="action-btn-icon">
                🔄
              </text>
            </view>
            <view class="action-btn" @tap="showSessionList = false">
              <text class="action-btn-icon">
                ✕
              </text>
            </view>
          </view>
        </view>

        <view class="session-list">
          <view
            v-for="session in sessions" :key="session.id" class="session-item"
            :class="{ active: session.id === currentSessionId }" @tap="handleSwitchSession(session.id)"
            @longpress="showSessionOptions(session.id)"
          >
            <view class="session-info">
              <text class="session-title">
                {{ session.title || '新的解读' }}
              </text>
              <text class="session-preview">
                {{ session.messages && session.messages.length > 0
                  ? `${session.messages[session.messages.length - 1].content.substring(0, 30)}...`
                  : '暂无消息'
                }}
              </text>
              <text class="session-time">
                {{ formatTime(session.lastActiveAt || session.createTime) }}
              </text>
            </view>
            <view class="session-actions">
              <view class="session-action-btn" @tap.stop="showSessionOptions(session.id)">
                <text class="session-action-icon">
                  ⋯
                </text>
              </view>
            </view>
          </view>

          <view v-if="sessions.length === 0" class="empty-sessions">
            <text class="empty-text">
              暂无解读会话
            </text>
            <view class="empty-btn" @tap="handleCreateNewSession">
              <text class="empty-btn-text">
                开始新解读
              </text>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 会话操作菜单 -->
    <wd-popup v-model="showSessionMenu" position="bottom" :style="{ height: '200rpx' }" class="absolute">
      <view class="session-menu">
        <view class="menu-item delete" @tap="handleDeleteSession">
          <text class="menu-icon">
            🗑️
          </text>
          <text class="menu-text">
            删除会话
          </text>
        </view>
        <view class="menu-item cancel" @tap="showSessionMenu = false">
          <text class="menu-text">
            取消
          </text>
        </view>
      </view>
    </wd-popup>

    <div class="flex-1 overflow-y-auto px-6 py-4 space-y-4">
      <!-- 加载状态 -->
      <div v-if="isLoading && messageList.length === 0" class="loading-container">
        <div class="loading-spinner" />
        <p class="loading-text">
          正在加载解读会话...
        </p>
      </div>

      <!-- 错误状态 -->
      <div v-if="error && messageList.length === 0" class="error-container">
        <p class="error-text">
          {{ error }}
        </p>
        <button class="retry-btn" @tap="initializeSessions">
          <span class="retry-text">
            重试
          </span>
        </button>
      </div>

      <!-- 消息列表 -->
      <div v-for="message in messageList" :key="message.id" class="message-item">
        <!-- AI消息 -->
        <div v-if="message.type === 'ai'" class="flex items-start space-x-3">
          <div
            class="h-8 w-8 flex flex-shrink-0 items-center justify-center overflow-hidden rounded-full from-purple-500 to-pink-500 bg-gradient-to-r"
          >
            <image
              src="https://kanli-1346109866.cos.ap-beijing.myqcloud.com/uploads/175326737359694e7ddfb-4405-4512-a16f-fcee1a09278e.jpg"
              mode="scaleToFill"
            />
          </div>
          <div class="message-bubble rounded-2xl rounded-tl-md bg-white p-4 shadow-sm">
            <div v-if="message.isLoading" class="loading-content">
              <div class="typing-indicator">
                <div class="dot" />
                <div class="dot" />
                <div class="dot" />
              </div>
              <p class="loading-text">
                AI正在解读中...
              </p>
            </div>
            <div v-else-if="message.isStreaming" class="streaming-content">
              <p class="text-sm text-gray-800 leading-relaxed">
                {{ message.content }}
              </p>
              <div class="streaming-indicator">
                <div class="streaming-main-info">
                  <span class="streaming-icon">
                    ⚡
                  </span>
                  <div class="streaming-info">
                    <span class="streaming-text">
                      微信流式传输中...
                    </span>
                    <span v-if="streamingChunkCount > 0" class="streaming-stats">
                      已接收 {{ streamingChunkCount }} 个数据块
                    </span>
                  </div>
                </div>
                <button
                  v-if="currentStreamingRequest && message.id === currentStreamingMessageId"
                  class="cancel-streaming-btn" @tap="cancelStreamingMessage"
                >
                  <span class="cancel-icon">
                    ✕
                  </span>
                </button>
              </div>
            </div>
            <p v-else class="text-sm text-gray-800 leading-relaxed">
              {{ message.content }}
            </p>
            <p class="mt-2 text-xs text-gray-500">
              {{ formatTime(message.timestamp) }}
            </p>
          </div>
        </div>

        <!-- 用户消息 -->
        <div v-else class="flex items-start justify-end space-x-3">
          <div class="message-bubble rounded-2xl rounded-tr-md bg-purple-600 p-4 text-white">
            <p class="text-sm leading-relaxed">
              {{ message.content }}
            </p>
            <p class="mt-2 text-xs text-purple-200">
              {{ formatTime(message.timestamp) }}
            </p>
          </div>
          <div class="h-8 w-8 flex flex-shrink-0 items-center justify-center rounded-full bg-gray-300">
            <i class="fas fa-user text-sm text-gray-600" />
          </div>
        </div>
      </div>

      <!-- 输入提示 -->
      <div v-if="messageList.length === 0 && !isLoading && !error" class="flex items-start space-x-3">
        <div
          class="h-8 w-8 flex flex-shrink-0 items-center justify-center rounded-full from-purple-500 to-pink-500 bg-gradient-to-r"
        >
          <i class="fas fa-robot text-sm text-white" />
        </div>
        <div class="message-bubble rounded-2xl rounded-tl-md bg-white p-4 shadow-sm">
          <p class="text-sm text-gray-800 leading-relaxed">
            AI原型解读
          </p>
          <p class="mt-2 text-xs text-gray-500">
            现在
          </p>
        </div>
      </div>
    </div>

    <!-- 快速提问 -->
    <div class="footer" style="position: sticky; bottom: 0; left: 0; right: 0 ">
      <div class="border-t border-gray-200 bg-white px-6 py-3">
        <div class="flex overflow-x-auto space-x-2">
          <button
            v-for="question in quickQuestions" :key="question"
            class="flex-shrink-0 rounded-full bg-gray-100 px-4 py-2 text-sm text-gray-700"
            :disabled="isAITyping" @tap="quickQuestion(question)"
          >
            {{ question }}
          </button>
        </div>
      </div>
      <!-- Message Input -->
      <div class="border-t border-gray-200 bg-white px-6 py-4">
        <div class="flex items-center space-x-3">
          <button class="text-gray-600" @tap="showSessionList = true">
            <view class="i-material-symbols:settings" />
          </button>
          <div class="relative flex-1">
            <input
              v-model="currentInput" type="text"
              class="w-full border border-gray-300 rounded-full px-4 py-3 pr-12 focus:border-transparent focus:ring-2 focus:ring-purple-500"
              placeholder="输入您的消息..." :disabled="isAITyping" @confirm="sendMessage"
            >
          </div>
          <button
            class="h-10 w-10 flex items-center justify-center rounded-full bg-purple-600 text-white"
            :class="{ 'opacity-50': !currentInput.trim() || isAITyping }"
            :disabled="!currentInput.trim() || isAITyping" @tap="sendMessage"
          >
            <view class="i-material-symbols:send-outline-rounded" />
          </button>
        </div>
      </div>
    </div>
  </view>
</template>

    <style lang="scss" scoped>
    /* 会话列表样式 */
.session-list-container {
  height: 88%;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.session-list-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.session-list-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.session-list-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
  background-color: #e0e0e0;
}

.action-btn-icon {
  font-size: 28rpx;
  color: #333333;
}

.session-list {
  flex: 1;
  overflow-y: auto;
  padding: 16rpx;
}

.session-item {
  padding: 24rpx;
  margin-bottom: 16rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.session-item.active {
  background-color: #e3f2fd;
  border-color: #007aff;
}

.session-item:active {
  transform: scale(0.98);
  background-color: #e0e0e0;
}

.session-info {
  flex: 1;
  margin-right: 16rpx;
}

.session-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.session-preview {
  font-size: 28rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.session-time {
  font-size: 24rpx;
  color: #999999;
  display: block;
}

.session-actions {
  display: flex;
  align-items: center;
}

.session-action-btn {
  width: 48rpx;
  height: 48rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.session-action-btn:active {
  transform: scale(0.9);
  background-color: #e0e0e0;
}

.session-action-icon {
  font-size: 24rpx;
  color: #666666;
}

.empty-sessions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  min-height: 400rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 32rpx;
}

.empty-btn {
  background-color: #007aff;
  padding: 24rpx 48rpx;
  border-radius: 48rpx;
  transition: all 0.3s ease;
}

.empty-btn:active {
  transform: scale(0.95);
  background-color: #0056b3;
}

.empty-btn-text {
  font-size: 28rpx;
  color: #ffffff;
}

/* 会话菜单样式 */
.session-menu {
  padding: 32rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.menu-item.delete {
  background-color: #ffebee;
  color: #d32f2f;
}

.menu-item.cancel {
  background-color: #f0f0f0;
  color: #666666;
}

.menu-item:active {
  transform: scale(0.98);
}

.menu-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.menu-text {
  font-size: 32rpx;
  font-weight: 500;
}

/* 加载和错误状态样式 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  min-height: 400rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 32rpx;
  color: #666666;
  text-align: center;
}

.error-text {
  font-size: 32rpx;
  color: #ff4444;
  text-align: center;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.retry-btn {
  background-color: #007aff;
  padding: 24rpx 48rpx;
  border-radius: 48rpx;
  transition: all 0.3s ease;
}

.retry-btn:active {
  transform: scale(0.95);
  background-color: #0056b3;
}

.retry-text {
  font-size: 28rpx;
  color: #ffffff;
}

/* 流式传输状态指示 */
.streaming-content {
  position: relative;
}

.streaming-indicator {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  padding: 4rpx 8rpx;
  background-color: #e3f2fd;
  border-radius: 8rpx;
  border: 1rpx solid #bbdefb;
}

.streaming-main-info {
  display: flex;
  align-items: center;
}

.streaming-icon {
  font-size: 24rpx;
  color: #007aff;
  margin-right: 6rpx;
  animation: pulse 1.5s infinite;
}

.streaming-info {
  display: flex;
  flex-direction: column;
}

.streaming-text {
  font-size: 20rpx;
  color: #007aff;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.streaming-stats {
  font-size: 20rpx;
  color: #666666;
}

.cancel-streaming-btn {
  margin-left: 8rpx;
  padding: 2rpx 6rpx;
  background-color: #ff4444;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24rpx;
  height: 24rpx;
  transition: all 0.3s ease;
}

.cancel-streaming-btn:active {
  transform: scale(0.9);
  background-color: #cc0000;
}

.cancel-icon {
  font-size: 18rpx;
  color: #ffffff;
  font-weight: bold;
  line-height: 1;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

/* 加载状态 */
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.typing-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #007aff;
  border-radius: 50%;
  margin: 0 4rpx;
  animation: typing 1.4s infinite ease-in-out;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 快速提问按钮样式 */
.footer button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.footer button:not(:disabled):active {
  transform: scale(0.95);
}
</style>
