<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '会员中心',
  },
}
</route>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'MembershipPage',
})

// 用户store
const userStore = useUserStore()

// 会员类型和价格
const membershipPlans = [
  {
    id: 'basic',
    name: '基础会员',
    price: 39,
    originalPrice: 59,
    duration: '1个月',
    color: '#20c997',
    features: [
      '基础心理测评无限次',
      '每月10次AI解读',
      '专业报告导出',
      '基础情绪跟踪',
    ],
  },
  {
    id: 'premium',
    name: '高级会员',
    price: 99,
    originalPrice: 159,
    duration: '3个月',
    color: '#6f42c1',
    features: [
      '高级心理测评无限次',
      '每月30次AI解读',
      '专业报告导出与分析',
      '完整情绪跟踪',
      '心理健康数据分析',
      '优先客服支持',
    ],
    isPopular: true,
  },
  {
    id: 'team',
    name: '团队版',
    price: 299,
    originalPrice: 399,
    duration: '6个月',
    color: '#007bff',
    features: [
      '高级心理测评无限次',
      '团队成员无限AI解读',
      '团队心理健康分析',
      '完整情绪跟踪与团队报告',
      '团队管理工具',
      '7x24小时专属客服',
    ],
  },
]

// 当前选择的会员计划
const selectedPlan = ref('premium')

// 选择支付方式
const paymentMethod = ref('wechat')

// 是否显示支付弹窗
const showPaymentModal = ref(false)

// 当前正在处理的订单
const currentOrder = ref<any>(null)

// 是否在支付过程中
const isProcessing = ref(false)

// 选择会员计划
function selectPlan(planId: string) {
  selectedPlan.value = planId
}

// 获取当前选择的计划
const currentPlan = computed(() => {
  return membershipPlans.find(plan => plan.id === selectedPlan.value) || membershipPlans[1]
})

// 格式化日期
function formatDate(dateString: string) {
  if (!dateString)
    return '未设置'
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 选择支付方式
function selectPaymentMethod(method: string) {
  paymentMethod.value = method
}

// 打开支付窗口
function openPaymentModal() {
  if (!userStore.isLoggedIn) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再购买会员',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login',
          })
        }
      },
    })
    return
  }

  // 创建订单
  currentOrder.value = {
    id: `ORDER_${Date.now()}`,
    plan: currentPlan.value,
    paymentMethod: paymentMethod.value,
    amount: currentPlan.value.price,
    createdAt: new Date().toISOString(),
  }

  showPaymentModal.value = true
}

// 关闭支付窗口
function closePaymentModal() {
  showPaymentModal.value = false
  currentOrder.value = null
}

// 模拟支付处理
async function processPayment() {
  if (!currentOrder.value || isProcessing.value)
    return

  isProcessing.value = true

  // 模拟支付过程
  try {
    // 等待1.5秒模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 更新会员状态(模拟)
    if (userStore.userDetails) {
      const expiryDate = new Date()
      let months = 1

      if (currentPlan.value.id === 'premium')
        months = 3
      else if (currentPlan.value.id === 'team')
        months = 6

      expiryDate.setMonth(expiryDate.getMonth() + months)

      const updatedUser = {
        ...userStore.userDetails,
        membershipType: currentPlan.value.id,
        membershipExpireDate: expiryDate.toISOString(),
        hasTeamPermission: currentPlan.value.id === 'team',
      }

      // 更新用户信息
      userStore.setUserDetails(updatedUser, userStore.token)

      // 关闭弹窗
      closePaymentModal()

      // 显示成功提示
      uni.showToast({
        title: '购买成功',
        icon: 'success',
        duration: 2000,
      })

      // 3秒后返回个人中心
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/profile/profile',
        })
      }, 3000)
    }
  }
  catch (error) {
    console.error('支付失败:', error)
    uni.showToast({
      title: '支付失败，请重试',
      icon: 'none',
    })
  }
  finally {
    isProcessing.value = false
  }
}
</script>

<template>
  <view class="membership-container">
    <!-- 顶部信息 -->
    <view class="header">
      <view class="header-content">
        <text class="header-title">
          会员中心
        </text>
        <text class="header-subtitle">
          升级会员，享受更多专业服务
        </text>

        <!-- 当前会员状态 -->
        <view v-if="userStore.isLoggedIn && userStore.userDetails" class="current-membership">
          <text class="current-label">
            当前会员:
          </text>
          <text
            class="current-type"
            :style="{ color: userStore.userDetails.membershipType === 'free' ? '#999' : '#6f42c1' }"
          >
            {{ userStore.userDetails.membershipType === 'free' ? '免费用户'
              : userStore.userDetails.membershipType === 'basic' ? '基础会员'
                : userStore.userDetails.membershipType === 'premium' ? '高级会员' : '团队版' }}
          </text>
          <text v-if="userStore.userDetails.membershipExpireDate" class="expiry-date">
            到期时间: {{ formatDate(userStore.userDetails.membershipExpireDate) }}
          </text>
        </view>
      </view>
    </view>

    <!-- 会员套餐选择 -->
    <view class="plans-container">
      <!-- 会员套餐卡片 -->
      <view
        v-for="plan in membershipPlans" :key="plan.id" class="plan-card"
        :class="{ active: selectedPlan === plan.id }" @tap="selectPlan(plan.id)"
      >
        <!-- 热门标签 -->
        <view v-if="plan.isPopular" class="popular-tag">
          <text>热门</text>
        </view>

        <!-- 计划信息 -->
        <view class="plan-header" :style="{ backgroundColor: `${plan.color}15` }">
          <view class="plan-name-container">
            <text class="plan-name" :style="{ color: plan.color }">
              {{ plan.name }}
            </text>
            <text class="plan-duration">
              {{ plan.duration }}
            </text>
          </view>

          <view class="plan-price-container">
            <text class="price-currency">
              ¥
            </text>
            <text class="price-amount">
              {{ plan.price }}
            </text>
            <text class="price-original">
              ¥{{ plan.originalPrice }}
            </text>
          </view>
        </view>

        <!-- 计划特性列表 -->
        <view class="plan-features">
          <view v-for="(feature, index) in plan.features" :key="index" class="feature-item">
            <text class="feature-icon">
              ✓
            </text>
            <text class="feature-text">
              {{ feature }}
            </text>
          </view>
        </view>

        <!-- 选择指示器 -->
        <view class="plan-selector">
          <view class="selector-circle" :class="{ checked: selectedPlan === plan.id }">
            <view v-if="selectedPlan === plan.id" class="selector-inner" />
          </view>
          <text class="selector-text">
            {{ selectedPlan === plan.id ? '已选择' : '选择此套餐' }}
          </text>
        </view>
      </view>
    </view>

    <!-- 支付方式选择 -->
    <view class="payment-methods">
      <text class="section-title">
        选择支付方式
      </text>

      <view class="payment-options">
        <view
          class="payment-option" :class="{ active: paymentMethod === 'wechat' }"
          @tap="selectPaymentMethod('wechat')"
        >
          <text class="payment-icon">
            💰
          </text>
          <text class="payment-name">
            微信支付
          </text>
          <view class="payment-selector">
            <view class="selector-circle" :class="{ checked: paymentMethod === 'wechat' }">
              <view v-if="paymentMethod === 'wechat'" class="selector-inner" />
            </view>
          </view>
        </view>

        <view
          class="payment-option" :class="{ active: paymentMethod === 'alipay' }"
          @tap="selectPaymentMethod('alipay')"
        >
          <text class="payment-icon">
            💳
          </text>
          <text class="payment-name">
            支付宝
          </text>
          <view class="payment-selector">
            <view class="selector-circle" :class="{ checked: paymentMethod === 'alipay' }">
              <view v-if="paymentMethod === 'alipay'" class="selector-inner" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 确认支付按钮 -->
    <view class="checkout-section">
      <view class="total-amount">
        <text class="total-label">
          总计:
        </text>
        <text class="total-value">
          ¥{{ currentPlan.price }}
        </text>
      </view>

      <button class="checkout-btn" @tap="openPaymentModal">
        立即开通
      </button>
    </view>
    <!-- 支付弹窗 -->
    <view v-if="showPaymentModal" class="payment-modal-overlay">
      <view class="payment-modal">
        <view class="modal-header">
          <text class="modal-title">
            确认支付
          </text>
          <text class="close-icon" @tap="closePaymentModal">
            ✕
          </text>
        </view>

        <view class="modal-content">
          <view class="modal-info">
            <text class="info-label">
              会员套餐:
            </text>
            <text class="info-value">
              {{ currentOrder.plan.name }} ({{ currentOrder.plan.duration }})
            </text>
          </view>

          <view class="modal-info">
            <text class="info-label">
              支付方式:
            </text>
            <text class="info-value">
              {{ currentOrder.paymentMethod === 'wechat' ? '微信支付' : '支付宝' }}
            </text>
          </view>

          <view class="modal-info">
            <text class="info-label">
              订单金额:
            </text>
            <text class="info-value price">
              ¥{{ currentOrder.amount }}
            </text>
          </view>

          <view class="qrcode-container">
            <view class="qrcode-placeholder">
              <text class="qrcode-text">
                模拟支付二维码
              </text>
            </view>
            <text class="qrcode-tip">
              请使用{{ currentOrder.paymentMethod === 'wechat' ? '微信' : '支付宝' }}扫码支付
            </text>
          </view>

          <button class="confirm-payment-btn" :disabled="isProcessing" @tap="processPayment">
            {{ isProcessing ? '处理中...' : '确认已支付' }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.membership-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 40rpx;
}

.header {
  background: linear-gradient(135deg, #6f42c1, #9370db);
  padding: 40rpx 32rpx;
  border-radius: 0 0 32rpx 32rpx;
  margin-bottom: 40rpx;
}

.header-content {
  color: #fff;
}

.header-title {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: block;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 24rpx;
  display: block;
}

.current-membership {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  margin-top: 24rpx;
}

.current-label {
  font-size: 24rpx;
  margin-right: 12rpx;
}

.current-type {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.expiry-date {
  font-size: 24rpx;
}

.plans-container {
  padding: 0 32rpx;
  margin-bottom: 40rpx;
}

.plan-card {
  background-color: #fff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  position: relative;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.plan-card.active {
  border-color: #6f42c1;
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(111, 66, 193, 0.2);
}

.popular-tag {
  position: absolute;
  top: 24rpx;
  right: -60rpx;
  background-color: #ff6b6b;
  color: white;
  transform: rotate(45deg);
  padding: 8rpx 60rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.plan-header {
  padding: 32rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.plan-name-container {
  display: flex;
  flex-direction: column;
}

.plan-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.plan-duration {
  font-size: 24rpx;
  color: #666;
}

.plan-price-container {
  display: flex;
  align-items: baseline;
}

.price-currency {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 4rpx;
}

.price-amount {
  font-size: 48rpx;
  font-weight: bold;
}

.price-original {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 12rpx;
}

.plan-features {
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-icon {
  color: #6f42c1;
  font-size: 28rpx;
  margin-right: 16rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #333;
}

.plan-selector {
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
}

.selector-circle {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #ccc;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selector-circle.checked {
  border-color: #6f42c1;
}

.selector-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #6f42c1;
}

.selector-text {
  font-size: 28rpx;
  color: #666;
}

.payment-methods {
  padding: 0 32rpx;
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.payment-options {
  display: flex;
  gap: 24rpx;
}

.payment-option {
  flex: 1;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 2rpx solid transparent;
}

.payment-option.active {
  border-color: #6f42c1;
}

.payment-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.payment-name {
  flex: 1;
  font-size: 28rpx;
}

.checkout-section {
  padding: 32rpx;
  background-color: #fff;
  position: sticky;
  bottom: 0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 10;
}

.total-amount {
  display: flex;
  align-items: baseline;
}

.total-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 12rpx;
}

.total-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #6f42c1;
}

.checkout-btn {
  background-color: #6f42c1;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  padding: 20rpx 48rpx;
  border-radius: 40rpx;
  border: none;
}

.benefits-section {
  padding: 40rpx 32rpx;
}

.benefit-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.benefit-item {
  width: calc(50% - 12rpx);
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.benefit-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.benefit-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.benefit-desc {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 支付弹窗 */
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-modal {
  width: 90%;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
}

.modal-header {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-icon {
  font-size: 32rpx;
  color: #999;
  padding: 12rpx;
}

.modal-content {
  padding: 32rpx;
}

.modal-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.info-value.price {
  color: #ff6b6b;
  font-weight: bold;
  font-size: 32rpx;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 32rpx 0;
}

.qrcode-placeholder {
  width: 240rpx;
  height: 240rpx;
  background-color: #f8f9fa;
  border: 2rpx dashed #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}

.qrcode-text {
  font-size: 24rpx;
  color: #999;
}

.qrcode-tip {
  font-size: 24rpx;
  color: #666;
}

.confirm-payment-btn {
  margin-top: 32rpx;
  background-color: #6f42c1;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  padding: 24rpx;
  border-radius: 48rpx;
  width: 100%;
  border: none;
}

.confirm-payment-btn:disabled {
  background-color: #b8a2db;
}
</style>
