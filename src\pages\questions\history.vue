<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '测评历史',
      },
    }
  </route>

<script setup lang="ts">
import type { ITestHistoryRecord } from '@/api/types/question'
import { onMounted, ref } from 'vue'
import { getTestHistory, getTestResultById } from '@/api/question'
import { useUserStore } from '@/store/user'

const historyList = ref<ITestHistoryRecord[]>([])
const loading = ref(false)
const page = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)

// 用户store
const userStore = useUserStore()

// 获取用户ID
function getUserId() {
  return userStore.userDetails?.id || 1
}

// 加载测试历史
async function loadHistory(isRefresh = false) {
  if (loading.value)
    return

  try {
    loading.value = true

    if (isRefresh) {
      page.value = 1
      historyList.value = []
      hasMore.value = true
    }

    const userId = getUserId()
    const response = await getTestHistory(userId, page.value, pageSize.value)

    if (response.data && response.data.data) {
      const newRecords = response.data.data || []
      const pagination = response.data.pagination

      if (isRefresh) {
        historyList.value = newRecords
      }
      else {
        historyList.value.push(...newRecords)
      }

      hasMore.value = pagination.hasNext
      if (hasMore.value) {
        page.value++
      }
    }
  }
  catch (error) {
    console.error('加载历史记录失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    })
  }
  finally {
    loading.value = false
  }
}

// 查看详细结果
function viewResult(record: ITestHistoryRecord) {
  // 转换数据结构以匹配结果页面的期望格式
  const resultData = {
    ...record,
    traits: generateTraits(record),
    strengths: generateStrengths(record),
    blindSpots: generateBlindSpots(record),
    suggestions: generateSuggestions(record),
    description: generateDescription(record),
  }

  const encodedData = encodeURIComponent(JSON.stringify(resultData))
  uni.navigateTo({
    url: `/pages/questions/results?result=${encodedData}`,
  })
}

// 根据MBTI和DISC分数生成性格特征
function generateTraits(record: ITestHistoryRecord): string[] {
  const traits: string[] = []

  // 基于MBTI类型添加特征
  if (record.mbtiType) {
    const mbtiTraits: { [key: string]: string[] } = {
      INTJ: ['战略思维', '独立分析', '追求完美'],
      INTP: ['逻辑思维', '创新探索', '深度思考'],
      ENTJ: ['领导能力', '果断决策', '目标导向'],
      ENTP: ['创新思维', '灵活应变', '挑战传统'],
      INFJ: ['洞察力强', '理想主义', '同理心'],
      INFP: ['理想主义', '创造力', '价值观导向'],
      ENFJ: ['领导魅力', '同理心', '激励他人'],
      ENFP: ['热情活力', '创新思维', '人际关系'],
      ISTJ: ['责任感', '实际务实', '组织能力'],
      ISFJ: ['服务精神', '细心周到', '忠诚可靠'],
      ESTJ: ['组织能力', '果断决策', '执行力强'],
      ESFJ: ['社交能力', '服务精神', '团队合作'],
      ISTP: ['灵活应变', '技术能力', '冷静分析'],
      ISFP: ['艺术感', '和谐追求', '实际务实'],
      ESTP: ['行动力强', '灵活应变', '冒险精神'],
      ESFP: ['活力四射', '社交能力', '享受当下'],
    }

    if (mbtiTraits[record.mbtiType]) {
      traits.push(...mbtiTraits[record.mbtiType])
    }
  }

  // 基于DISC类型添加特征
  if (record.discType) {
    const discTraits: { [key: string]: string[] } = {
      D: ['目标导向', '果断决策', '竞争意识'],
      I: ['人际导向', '乐观积极', '影响力'],
      S: ['稳定可靠', '团队合作', '耐心细致'],
      C: ['精确分析', '质量导向', '系统思维'],
    }

    if (discTraits[record.discType]) {
      traits.push(...discTraits[record.discType])
    }
  }

  return traits.length > 0 ? traits : ['分析思维', '目标导向', '团队合作']
}

// 生成优势描述
function generateStrengths(record: ITestHistoryRecord): string[] {
  const strengths: string[] = []

  if (record.mbtiScores) {
    if (record.mbtiScores.I > record.mbtiScores.E) {
      strengths.push('深度思考能力强')
    }
    if (record.mbtiScores.N > record.mbtiScores.S) {
      strengths.push('创新思维突出')
    }
    if (record.mbtiScores.T > record.mbtiScores.F) {
      strengths.push('逻辑分析能力强')
    }
    if (record.mbtiScores.P > record.mbtiScores.J) {
      strengths.push('灵活应变能力强')
    }
  }

  if (record.discScores) {
    const scores = Object.values(record.discScores) as number[]
    const maxScore = Math.max(...scores)
    if (record.discScores.D === maxScore) {
      strengths.push('领导决策能力强')
    }
    if (record.discScores.I === maxScore) {
      strengths.push('人际沟通能力强')
    }
    if (record.discScores.S === maxScore) {
      strengths.push('团队协作能力强')
    }
    if (record.discScores.C === maxScore) {
      strengths.push('专业分析能力强')
    }
  }

  return strengths.length > 0 ? strengths : ['学习能力强', '适应能力强', '团队合作精神']
}

// 生成盲点描述
function generateBlindSpots(record: ITestHistoryRecord): string[] {
  const blindSpots: string[] = []

  if (record.mbtiScores) {
    if (record.mbtiScores.E > record.mbtiScores.I) {
      blindSpots.push('可能忽视深度思考')
    }
    if (record.mbtiScores.S > record.mbtiScores.N) {
      blindSpots.push('可能缺乏创新思维')
    }
    if (record.mbtiScores.F > record.mbtiScores.T) {
      blindSpots.push('可能过于感性决策')
    }
    if (record.mbtiScores.J > record.mbtiScores.P) {
      blindSpots.push('可能缺乏灵活性')
    }
  }

  return blindSpots.length > 0 ? blindSpots : ['需要提升沟通技巧', '需要增强时间管理', '需要培养创新思维']
}

// 生成建议
function generateSuggestions(record: ITestHistoryRecord): string[] {
  const suggestions: string[] = []

  if (record.mbtiScores) {
    if (record.mbtiScores.I > record.mbtiScores.E) {
      suggestions.push('多参与团队活动，提升社交能力')
    }
    if (record.mbtiScores.S > record.mbtiScores.N) {
      suggestions.push('尝试创新思维，拓展可能性')
    }
    if (record.mbtiScores.T > record.mbtiScores.F) {
      suggestions.push('关注他人感受，提升情商')
    }
    if (record.mbtiScores.J > record.mbtiScores.P) {
      suggestions.push('保持开放心态，接受变化')
    }
  }

  return suggestions.length > 0 ? suggestions : ['持续学习新技能', '培养领导能力', '加强团队合作']
}

// 生成描述
function generateDescription(record: ITestHistoryRecord): string {
  let description = `您是一个${record.mbtiType}型性格的人，具有${record.discType}型的行为特征。`

  if (record.mbtiScores) {
    if (record.mbtiScores.I > record.mbtiScores.E) {
      description += '您倾向于内向思考，喜欢深度分析问题。'
    }
    else {
      description += '您倾向于外向交流，善于与人互动。'
    }

    if (record.mbtiScores.N > record.mbtiScores.S) {
      description += '您更关注可能性和创新，喜欢探索新事物。'
    }
    else {
      description += '您更关注具体细节和实际经验，注重实用性。'
    }

    if (record.mbtiScores.T > record.mbtiScores.F) {
      description += '您在做决策时更依赖逻辑分析。'
    }
    else {
      description += '您在做决策时更考虑他人感受。'
    }

    if (record.mbtiScores.P > record.mbtiScores.J) {
      description += '您喜欢保持选择的开放性，灵活应对变化。'
    }
    else {
      description += '您喜欢制定计划，追求有序和确定性。'
    }
  }

  return description
}

// 重新开始测试
function startNewTest() {
  uni.navigateTo({
    url: '/pages/questions/questions',
  })
}

// 下拉刷新
function onRefresh() {
  loadHistory(true)
}

// 上拉加载更多
function onLoadMore() {
  if (hasMore.value && !loading.value) {
    loadHistory(false)
  }
}

// 格式化日期
function formatDate(dateString: string) {
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 获取类型颜色
function getTypeColor(type: string) {
  const colors = {
    INTJ: '#667eea',
    INFJ: '#764ba2',
    INFP: '#f093fb',
    INTP: '#f5576c',
    ENTJ: '#4facfe',
    ENTP: '#00f2fe',
    ENFJ: '#43e97b',
    ENFP: '#38f9d7',
    ISTJ: '#667eea',
    ISFJ: '#764ba2',
    ISFP: '#f093fb',
    ISTP: '#f5576c',
    ESTJ: '#4facfe',
    ESTP: '#00f2fe',
    ESFJ: '#43e97b',
    ESFP: '#38f9d7',
  }
  return colors[type] || '#1fa474'
}

// 检查登录状态
function checkLoginStatus() {
  if (!userStore.isLoggedIn) {
    uni.showModal({
      title: '提示',
      content: '请先登录后查看测评历史',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login',
          })
        }
        else {
          uni.navigateBack()
        }
      },
    })
    return false
  }
  return true
}

onMounted(() => {
  if (checkLoginStatus()) {
    loadHistory(true)
  }
})
</script>

<template>
  <view class="page-main">
    <!-- 头部统计 -->
    <view class="header-stats">
      <view class="stat-item">
        <view class="stat-number">
          {{ historyList.length }}
        </view>
        <view class="stat-label">
          测评次数
        </view>
      </view>
      <view class="stat-divider" />
      <view class="stat-item">
        <view class="stat-number">
          {{ historyList.length > 0 ? 1 : 0 }}
        </view>
        <view class="stat-label">
          当前类型
        </view>
      </view>
    </view>

    <!-- 历史记录列表 -->
    <view class="history-container">
      <view v-if="loading && historyList.length === 0" class="loading-container">
        <view class="loading-text">
          正在加载历史记录...
        </view>
      </view>

      <view v-else-if="historyList.length === 0" class="empty-container">
        <view class="empty-icon">
          📊
        </view>
        <view class="empty-text">
          暂无测评记录
        </view>
        <view class="empty-desc">
          完成第一次性格测评，了解真实的自己
        </view>
        <view class="action-btn primary" @click="startNewTest">
          开始测评
        </view>
      </view>

      <view v-else class="history-list">
        <view
          v-for="record in historyList"
          :key="record.id"
          class="history-item"
          @click="viewResult(record)"
        >
          <view class="item-header">
            <view class="item-date">
              {{ formatDate(record.createdAt) }}
            </view>
            <view class="item-types">
              <view
                class="type-tag mbti"
                :style="{ backgroundColor: getTypeColor(record.mbtiType) }"
              >
                {{ record.mbtiType }}
              </view>
              <view class="type-tag disc">
                {{ record.discType }}
              </view>
            </view>
          </view>

          <view class="item-content">
            <view class="item-title">
              {{ record.finalType }}
            </view>
            <view class="item-desc">
              {{ generateDescription(record).slice(0, 60) }}...
            </view>
          </view>

          <view class="item-footer">
            <view class="item-traits">
              <view
                v-for="trait in generateTraits(record).slice(0, 3)"
                :key="trait"
                class="trait-tag"
              >
                {{ trait }}
              </view>
            </view>
            <view class="view-detail">
              查看详情 →
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="hasMore" class="load-more" @click="onLoadMore">
          <view v-if="loading" class="loading-text">
            加载中...
          </view>
          <view v-else class="load-more-text">
            点击加载更多
          </view>
        </view>

        <view v-else-if="historyList.length > 0" class="no-more">
          没有更多记录了
        </view>
      </view>
    </view>

    <!-- 底部操作 -->
    <view class="bottom-actions">
      <view class="action-btn secondary" @click="onRefresh">
        刷新记录
      </view>
      <view class="action-btn primary" @click="startNewTest">
        重新测评
      </view>
    </view>
  </view>
</template>

  <style scoped>
  .page-main {
  width: 100%;
  min-height: 100vh;
  background: #f5f7fa;
}

.header-stats {
  background: #ffffff;
  padding: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #1fa474;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background: #e5e7eb;
  margin: 0 40rpx;
}

.history-container {
  flex: 1;
  padding: 24rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 40rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.item-date {
  font-size: 24rpx;
  color: #666666;
}

.item-types {
  display: flex;
  gap: 8rpx;
}

.type-tag {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
  color: #ffffff;
}

.type-tag.mbti {
  /* 颜色通过内联样式设置 */
}

.type-tag.disc {
  background: #f59e0b;
}

.item-content {
  margin-bottom: 16rpx;
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.item-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-traits {
  display: flex;
  gap: 8rpx;
  flex: 1;
}

.trait-tag {
  background: #f0f9ff;
  color: #0369a1;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.view-detail {
  font-size: 24rpx;
  color: #1fa474;
}

.load-more {
  text-align: center;
  padding: 32rpx;
}

.load-more-text {
  font-size: 28rpx;
  color: #1fa474;
}

.no-more {
  text-align: center;
  padding: 32rpx;
  font-size: 24rpx;
  color: #999999;
}

.bottom-actions {
  display: flex;
  gap: 16rpx;
  padding: 24rpx;
  background: #ffffff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
}

.action-btn.primary {
  background: linear-gradient(135deg, #1fa474 0%, #26d0ce 100%);
  color: #ffffff;
}

.action-btn.secondary {
  background: #ffffff;
  color: #1fa474;
  border: 2rpx solid #1fa474;
}
</style>
