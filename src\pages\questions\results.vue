<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '测评结果',
      },
    }
  </route>

<script setup lang="ts">
import type { IPersonalityResult } from '@/api/types/question'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'

const result = ref<IPersonalityResult | null>(null)
const loading = ref(false)

// 页面加载时获取结果数据
onLoad((options) => {
  if (options.result) {
    try {
      const rawResult = JSON.parse(decodeURIComponent(options.result))
      // 转换数据结构并添加默认描述
      result.value = {
        ...rawResult,
        traits: generateTraits(rawResult),
        strengths: generateStrengths(rawResult),
        blindSpots: generateBlindSpots(rawResult),
        suggestions: generateSuggestions(rawResult),
        description: generateDescription(rawResult),
      }
    }
    catch (error) {
      console.error('解析结果数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
    }
  }
})

// 根据MBTI和DISC分数生成性格特征
function generateTraits(result: any): string[] {
  const traits: string[] = []

  // 基于MBTI类型添加特征
  if (result.mbtiType) {
    const mbtiTraits: { [key: string]: string[] } = {
      INTJ: ['战略思维', '独立分析', '追求完美'],
      INTP: ['逻辑思维', '创新探索', '深度思考'],
      ENTJ: ['领导能力', '果断决策', '目标导向'],
      ENTP: ['创新思维', '灵活应变', '挑战传统'],
      INFJ: ['洞察力强', '理想主义', '同理心'],
      INFP: ['理想主义', '创造力', '价值观导向'],
      ENFJ: ['领导魅力', '同理心', '激励他人'],
      ENFP: ['热情活力', '创新思维', '人际关系'],
      ISTJ: ['责任感', '实际务实', '组织能力'],
      ISFJ: ['服务精神', '细心周到', '忠诚可靠'],
      ESTJ: ['组织能力', '果断决策', '执行力强'],
      ESFJ: ['社交能力', '服务精神', '团队合作'],
      ISTP: ['灵活应变', '技术能力', '冷静分析'],
      ISFP: ['艺术感', '和谐追求', '实际务实'],
      ESTP: ['行动力强', '灵活应变', '冒险精神'],
      ESFP: ['活力四射', '社交能力', '享受当下'],
    }

    if (mbtiTraits[result.mbtiType]) {
      traits.push(...mbtiTraits[result.mbtiType])
    }
  }

  // 基于DISC类型添加特征
  if (result.discType) {
    const discTraits: { [key: string]: string[] } = {
      D: ['目标导向', '果断决策', '竞争意识'],
      I: ['人际导向', '乐观积极', '影响力'],
      S: ['稳定可靠', '团队合作', '耐心细致'],
      C: ['精确分析', '质量导向', '系统思维'],
    }

    if (discTraits[result.discType]) {
      traits.push(...discTraits[result.discType])
    }
  }

  return traits.length > 0 ? traits : ['分析思维', '目标导向', '团队合作']
}

// 生成优势描述
function generateStrengths(result: any): string[] {
  const strengths: string[] = []

  if (result.mbtiScores) {
    if (result.mbtiScores.I > result.mbtiScores.E) {
      strengths.push('深度思考能力强')
    }
    if (result.mbtiScores.N > result.mbtiScores.S) {
      strengths.push('创新思维突出')
    }
    if (result.mbtiScores.T > result.mbtiScores.F) {
      strengths.push('逻辑分析能力强')
    }
    if (result.mbtiScores.P > result.mbtiScores.J) {
      strengths.push('灵活应变能力强')
    }
  }

  if (result.discScores) {
    const scores = Object.values(result.discScores) as number[]
    const maxScore = Math.max(...scores)
    if (result.discScores.D === maxScore) {
      strengths.push('领导决策能力强')
    }
    if (result.discScores.I === maxScore) {
      strengths.push('人际沟通能力强')
    }
    if (result.discScores.S === maxScore) {
      strengths.push('团队协作能力强')
    }
    if (result.discScores.C === maxScore) {
      strengths.push('专业分析能力强')
    }
  }

  return strengths.length > 0 ? strengths : ['学习能力强', '适应能力强', '团队合作精神']
}

// 生成盲点描述
function generateBlindSpots(result: any): string[] {
  const blindSpots: string[] = []

  if (result.mbtiScores) {
    if (result.mbtiScores.E > result.mbtiScores.I) {
      blindSpots.push('可能忽视深度思考')
    }
    if (result.mbtiScores.S > result.mbtiScores.N) {
      blindSpots.push('可能缺乏创新思维')
    }
    if (result.mbtiScores.F > result.mbtiScores.T) {
      blindSpots.push('可能过于感性决策')
    }
    if (result.mbtiScores.J > result.mbtiScores.P) {
      blindSpots.push('可能缺乏灵活性')
    }
  }

  return blindSpots.length > 0 ? blindSpots : ['需要提升沟通技巧', '需要增强时间管理', '需要培养创新思维']
}

// 生成建议
function generateSuggestions(result: any): string[] {
  const suggestions: string[] = []

  if (result.mbtiScores) {
    if (result.mbtiScores.I > result.mbtiScores.E) {
      suggestions.push('多参与团队活动，提升社交能力')
    }
    if (result.mbtiScores.S > result.mbtiScores.N) {
      suggestions.push('尝试创新思维，拓展可能性')
    }
    if (result.mbtiScores.T > result.mbtiScores.F) {
      suggestions.push('关注他人感受，提升情商')
    }
    if (result.mbtiScores.J > result.mbtiScores.P) {
      suggestions.push('保持开放心态，接受变化')
    }
  }

  return suggestions.length > 0 ? suggestions : ['持续学习新技能', '培养领导能力', '加强团队合作']
}

// 生成描述
function generateDescription(result: any): string {
  let description = `您是一个${result.mbtiType}型性格的人，具有${result.discType}型的行为特征。`

  if (result.mbtiScores) {
    if (result.mbtiScores.I > result.mbtiScores.E) {
      description += '您倾向于内向思考，喜欢深度分析问题。'
    }
    else {
      description += '您倾向于外向交流，善于与人互动。'
    }

    if (result.mbtiScores.N > result.mbtiScores.S) {
      description += '您更关注可能性和创新，喜欢探索新事物。'
    }
    else {
      description += '您更关注具体细节和实际经验，注重实用性。'
    }

    if (result.mbtiScores.T > result.mbtiScores.F) {
      description += '您在做决策时更依赖逻辑分析。'
    }
    else {
      description += '您在做决策时更考虑他人感受。'
    }

    if (result.mbtiScores.P > result.mbtiScores.J) {
      description += '您喜欢保持选择的开放性，灵活应对变化。'
    }
    else {
      description += '您喜欢制定计划，追求有序和确定性。'
    }
  }

  return description
}

// 查看历史记录
function goToHistory() {
  uni.navigateTo({
    url: '/pages/questions/history',
  })
}

// 重新测试
function retakeTest() {
  uni.navigateTo({
    url: '/pages/questions/questions',
  })
}

// 分享结果
function shareResult() {
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none',
  })
}

// 返回首页
function goHome() {
  uni.switchTab({
    url: '/pages/index/index',
  })
}
</script>

<template>
  <view class="page-main">
    <view v-if="loading" class="loading-container">
      <view class="loading-text">
        正在加载结果...
      </view>
    </view>

    <view v-else-if="result" class="result-container">
      <!-- 结果头部 -->
      <view class="result-header">
        <view class="result-title">
          您的性格类型
        </view>
        <view class="result-types">
          <view class="type-item">
            <view class="type-label">
              MBTI类型
            </view>
            <view class="type-value mbti">
              {{ result.mbtiType }}
            </view>
          </view>
          <view class="type-item">
            <view class="type-label">
              DISC类型
            </view>
            <view class="type-value disc">
              {{ result.discType }}
            </view>
          </view>
          <view class="type-item">
            <view class="type-label">
              综合类型
            </view>
            <view class="type-value final">
              {{ result.finalType }}
            </view>
          </view>
        </view>
      </view>

      <!-- 详细描述 -->
      <view class="result-section">
        <view class="section-title">
          性格描述
        </view>
        <view class="section-content">
          {{ result.description }}
        </view>
      </view>

      <!-- 性格特征 -->
      <view class="result-section">
        <view class="section-title">
          性格特征
        </view>
        <view class="traits-container">
          <view v-for="trait in result.traits" :key="trait" class="trait-item">
            {{ trait }}
          </view>
        </view>
      </view>

      <!-- 优势 -->
      <view class="result-section">
        <view class="section-title">
          您的优势
        </view>
        <view class="list-container">
          <view v-for="(strength, index) in result.strengths" :key="index" class="strength list-item">
            <view class="list-icon">
              ✓
            </view>
            <view class="list-text">
              {{ strength }}
            </view>
          </view>
        </view>
      </view>

      <!-- 盲点 -->
      <view class="result-section">
        <view class="section-title">
          需要注意的盲点
        </view>
        <view class="list-container">
          <view v-for="(blindSpot, index) in result.blindSpots" :key="index" class="blindspot list-item">
            <view class="list-icon">
              !
            </view>
            <view class="list-text">
              {{ blindSpot }}
            </view>
          </view>
        </view>
      </view>

      <!-- 建议 -->
      <view class="result-section">
        <view class="section-title">
          发展建议
        </view>
        <view class="list-container">
          <view v-for="(suggestion, index) in result.suggestions" :key="index" class="suggestion list-item">
            <view class="list-icon">
              →
            </view>
            <view class="list-text">
              {{ suggestion }}
            </view>
          </view>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view class="action-buttons">
        <view class="action-btn secondary" @click="goToHistory">
          查看历史
        </view>
        <view class="action-btn secondary" @click="retakeTest">
          重新测试
        </view>
        <view class="action-btn primary" @click="shareResult">
          分享结果
        </view>
        <view class="action-btn primary" @click="goHome">
          返回首页
        </view>
      </view>
    </view>

    <!-- 无结果状态 -->
    <view v-else class="no-result">
      <view class="no-result-text">
        未找到测评结果
      </view>
      <view class="action-btn primary" @click="retakeTest">
        开始测评
      </view>
    </view>
  </view>
</template>

  <style scoped>
  .page-main {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-text {
  font-size: 32rpx;
  color: #666666;
}

.result-container {
  padding: 40rpx 32rpx;
}

.result-header {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-bottom: 32rpx;
}

.result-types {
  display: flex;
  justify-content: space-around;
}

.type-item {
  text-align: center;
}

.type-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.type-value {
  font-size: 32rpx;
  font-weight: bold;
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  color: #ffffff;
}

.type-value.mbti {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.type-value.disc {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.type-value.final {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.result-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: #1fa474;
  border-radius: 3rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

.traits-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.trait-item {
  background: #f0f9ff;
  color: #0369a1;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  border: 2rpx solid #bae6fd;
}

.list-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.list-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.list-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  color: #ffffff;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.list-item.strength .list-icon {
  background: #10b981;
}

.list-item.blindspot .list-icon {
  background: #f59e0b;
}

.list-item.suggestion .list-icon {
  background: #3b82f6;
}

.list-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  flex: 1;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-top: 40rpx;
}

.action-btn {
  height: 88rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
}

.action-btn.primary {
  background: linear-gradient(135deg, #1fa474 0%, #26d0ce 100%);
  color: #ffffff;
}

.action-btn.secondary {
  background: #ffffff;
  color: #1fa474;
  border: 2rpx solid #1fa474;
}

.no-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
}

.no-result-text {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 40rpx;
}
</style>
