<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '注册',
      },
    }
    </route>

<script lang="ts" setup>
import { ref } from 'vue'

defineOptions({
  name: 'Register',
})

function goBack() {
  uni.navigateBack()
}

// 注册表单数据
const formData = ref({
  phone: '',
  verificationCode: '',
  password: '',
  confirmPassword: '',
})

// 密码显示/隐藏状态
const showPassword = ref(false)
const showConfirmPassword = ref(false)

// 切换密码显示状态
function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}

// 切换确认密码显示状态
function toggleConfirmPasswordVisibility() {
  showConfirmPassword.value = !showConfirmPassword.value
}

// 发送验证码
const countdown = ref(0)
const timer = ref<number | null>(null)

function sendVerificationCode() {
  if (countdown.value > 0)
    return
  if (!formData.value.phone) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none',
    })
    return
  }

  // 模拟发送验证码
  uni.showToast({
    title: '验证码发送成功',
    icon: 'success',
  })

  // 开始倒计时
  countdown.value = 60
  timer.value = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer.value as number)
      timer.value = null
    }
  }, 1000)
}

// 提交注册
function submitRegister() {
  // 表单验证
  if (!formData.value.phone) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none',
    })
    return
  }

  if (!formData.value.verificationCode) {
    uni.showToast({
      title: '请输入验证码',
      icon: 'none',
    })
    return
  }

  if (!formData.value.password) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none',
    })
    return
  }

  if (formData.value.password !== formData.value.confirmPassword) {
    uni.showToast({
      title: '两次密码输入不一致',
      icon: 'none',
    })
    return
  }

  // 提交注册请求
  uni.showLoading({
    title: '注册中...',
  })

  // 模拟注册成功
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '注册成功',
      icon: 'success',
    })
    // 注册成功后跳转
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }, 1500)
}

// 获取屏幕边界到安全区域距离
let safeAreaInsets
let systemInfo
// #ifdef MP-WEIXIN
// 微信小程序使用新的API
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif
</script>

<template>
  <view
    class="h-full" :style="{
      marginTop: `${safeAreaInsets?.top}px`,
    }"
  >
    <view class="i-material-symbols:arrow-back-ios-rounded" style="margin-left: 20rpx;" @click="goBack" />
  </view>
  <view
    class="main-content" :style="{
      height: `calc(100% - ${safeAreaInsets?.top}px)`,
    }"
  >
    <!-- 浮动粒子效果 -->
    <view class="floating-particles">
      <view class="particle" />
      <view class="particle" />
      <view class="particle" />
      <view class="particle" />
    </view>

    <view class="login-container">
      <!-- 应用头部 -->
      <!-- 注册表单 -->
      <view class="login-form">
        <h2 class="form-title">
          注册
        </h2>

        <!-- 手机号 -->
        <view class="form-group">
          <label class="form-label">手机号</label>
          <input v-model="formData.phone" class="form-input" placeholder="请输入手机号" :maxlength="11">
        </view>

        <!-- 验证码 -->
        <view class="form-group">
          <label class="form-label">验证码</label>
          <view class="verification-code-container">
            <input
              v-model="formData.verificationCode" class="form-input verification-input" placeholder="请输入验证码"
              :maxlength="6"
            >
            <button class="verification-code-button" :disabled="countdown > 0" @click="sendVerificationCode">
              {{ countdown > 0 ? `${countdown}秒后重发` : '获取验证码' }}
            </button>
          </view>
        </view>

        <!-- 密码 -->
        <view class="form-group">
          <label class="form-label">密码</label>
          <view class="password-input-container">
            <input v-model="formData.password" class="form-input" placeholder="请输入密码" :password="!showPassword">
            <!-- 密码显示隐藏图标 -->
            <view class="password-toggle" @click="togglePasswordVisibility">
              <!-- <text class="i-material-symbols-outlined">
                {{ showPassword ? 'visibility_off' : 'visibility' }}
              </text> -->
            </view>
          </view>
        </view>

        <!-- 确认密码 -->
        <view class="form-group">
          <label class="form-label">确认密码</label>
          <view class="password-input-container">
            <input
              v-model="formData.confirmPassword" class="form-input" placeholder="请再次输入密码"
              :password="!showConfirmPassword"
            >
            <!-- 密码显示隐藏图标 -->
            <view class="password-toggle" @click="toggleConfirmPasswordVisibility">
              <!-- <text class="i-material-symbols-outlined">
                {{ showConfirmPassword ? 'visibility_off' : 'visibility' }}
              </text> -->
            </view>
          </view>
        </view>

        <button class="login-button" @click="submitRegister">
          注册
        </button>

        <!-- #ifndef MP-WEIXIN -->
        <view class="social-login">
          <button class="social-button">
            <text class="material-symbols-outlined wechat">
              chat
            </text>
            微信
          </button>
          <button class="social-button">
            <text class="material-symbols-outlined apple">
              phone_iphone
            </text>
            Apple
          </button>
          <button class="social-button">
            <text class="material-symbols-outlined qq">
              forum
            </text>
            QQ
          </button>
        </view>
        <!-- #endif -->
      </view>

      <!-- 快速体验 -->
      <!-- <view class="quick-login">
            <h3 class="quick-login-title">
              快速体验
            </h3>
            <view class="quick-login-buttons">
              <button class="quick-button">
                游客模式
              </button>
              <button class="quick-button">
                免费测评
              </button>
            </view>
          </view> -->

      <!-- 注册链接 -->
      <!-- <view class="signup-link">
            <text class="signup-text">
              还没有账号？
            </text>
            <a href="register.html" class="signup-button">立即注册</a>
          </view> -->
    </view>
  </view>
</template>

<style lang="scss">
@import './register.css';

/* 添加验证码输入框样式 */
.verification-code-container {
  display: flex;
  gap: 12px;
}

.verification-input {
  flex: 1;
}

.verification-code-button {
  width: 200rpx;
  height: 48px;
  background: #f0f0f0;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  color: #333;
  padding: 0 10px;
  white-space: nowrap;

  &:disabled {
    color: #999;
    background: #e8e8e8;
  }
}
</style>
